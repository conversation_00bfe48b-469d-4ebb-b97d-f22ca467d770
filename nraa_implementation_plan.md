# NRAA Migration Implementation Plan

## Phase 1: Foundation Setup (Weeks 1-6)

### Week 1-2: Project Setup & Database Analysis
```bash
# 1. Initialize new project structure
mkdir nraa-modern
cd nraa-modern

# Backend setup
mkdir backend
cd backend
npm init -y
npm install express typescript prisma @prisma/client
npm install -D @types/node @types/express ts-node nodemon

# Frontend setup
cd ../
npx create-next-app@latest frontend --typescript --tailwind --app
cd frontend
npm install @tanstack/react-query zustand react-hook-form @hookform/resolvers zod
```

### Week 2-3: Database Schema & Prisma Setup
```typescript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Shooter {
  id       Int     @id @default(autoincrement())
  sid      Int?    @unique
  club     String?
  old_uin  String?
  status   ShooterStatus @default(Active)
  
  // Relations
  details     ShooterDetails?
  membership  ShooterMembership?
  addresses   ShooterAddress[]
  results     Result[]
  
  @@map("shooters")
}

model ShooterDetails {
  shooter_id           Int      @id
  email               String
  title               String
  first_name          String
  middle_name         String?
  last_name           String
  preferred_name      String?
  gender              Gender?
  date_of_birth       Int?
  home_phone          String?
  mobile_phone        String?
  is_right_handed     Boolean  @default(true)
  is_using_bench      Boolean  @default(false)
  is_coach            Boolean  @default(false)
  is_competition_coach Boolean @default(false)
  special_flags       Int      @default(0)
  
  // Relations
  shooter Shooter @relation(fields: [shooter_id], references: [id])
  
  @@map("shooter_details")
}

model Event {
  id             Int      @id @default(autoincrement())
  name           String
  start_date     Int
  end_date       Int
  location       String
  association_id Int?
  is_competition Boolean  @default(true)
  is_team_event  Boolean  @default(false)
  is_queens_event Boolean @default(false)
  is_ranked      Boolean  @default(false)
  is_enter_shots Boolean
  is_divisional  Boolean  @default(false)
  
  // Relations
  association Association? @relation(fields: [association_id], references: [id])
  matches     Match[]
  
  @@map("events")
}

model Match {
  id           Int     @id @default(autoincrement())
  event_id     Int
  number       Int
  name         String  @default("")
  is_graded    Boolean @default(false)
  is_published Boolean @default(false)
  is_cancelled Boolean @default(false)
  cancel_reason String?
  
  // Relations
  event   Event    @relation(fields: [event_id], references: [id])
  results Result[]
  
  @@map("matches")
}

model Result {
  id            Int @id @default(autoincrement())
  match_id      Int
  shooter_id    Int
  grade_id      Int
  place         Int
  shots         String?
  score_whole   Int
  score_partial Int
  
  // Relations
  match   Match   @relation(fields: [match_id], references: [id])
  shooter Shooter @relation(fields: [shooter_id], references: [id])
  grade   Grade   @relation(fields: [grade_id], references: [id])
  
  @@map("results")
}

enum ShooterStatus {
  Active
  Inactive
  Deceased
  Honorary
}

enum Gender {
  male
  female
}
```

### Week 3-4: Backend API Foundation
```typescript
// backend/src/app.ts
import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import shooterRoutes from './routes/shooters';
import eventRoutes from './routes/events';
import authRoutes from './routes/auth';

const app = express();
const prisma = new PrismaClient();

app.use(cors());
app.use(express.json());

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/shooters', shooterRoutes);
app.use('/api/events', eventRoutes);

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

export { app, prisma };
```

```typescript
// backend/src/routes/shooters.ts
import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../app';

const router = Router();

const shooterSchema = z.object({
  sid: z.number().optional(),
  club: z.string().optional(),
  status: z.enum(['Active', 'Inactive', 'Deceased', 'Honorary']),
  details: z.object({
    email: z.string().email(),
    first_name: z.string(),
    last_name: z.string(),
    // ... other fields
  }).optional()
});

// GET /api/shooters - List shooters with pagination
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 50;
    const search = req.query.search as string;
    
    const where = search ? {
      OR: [
        { sid: { equals: parseInt(search) || undefined } },
        { details: { 
          OR: [
            { first_name: { contains: search } },
            { last_name: { contains: search } },
            { email: { contains: search } }
          ]
        }}
      ]
    } : {};
    
    const [shooters, total] = await Promise.all([
      prisma.shooter.findMany({
        where,
        include: {
          details: true,
          membership: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { sid: 'asc' }
      }),
      prisma.shooter.count({ where })
    ]);
    
    res.json({
      data: shooters,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch shooters' });
  }
});

// GET /api/shooters/:id - Get single shooter
router.get('/:id', async (req, res) => {
  try {
    const shooter = await prisma.shooter.findUnique({
      where: { id: parseInt(req.params.id) },
      include: {
        details: true,
        membership: true,
        addresses: true,
        results: {
          include: {
            match: {
              include: { event: true }
            },
            grade: true
          }
        }
      }
    });
    
    if (!shooter) {
      return res.status(404).json({ error: 'Shooter not found' });
    }
    
    res.json(shooter);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch shooter' });
  }
});

// POST /api/shooters - Create new shooter
router.post('/', async (req, res) => {
  try {
    const validatedData = shooterSchema.parse(req.body);
    
    const shooter = await prisma.shooter.create({
      data: {
        sid: validatedData.sid,
        club: validatedData.club,
        status: validatedData.status,
        details: validatedData.details ? {
          create: validatedData.details
        } : undefined
      },
      include: {
        details: true,
        membership: true
      }
    });
    
    res.status(201).json(shooter);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Validation failed', details: error.errors });
    }
    res.status(500).json({ error: 'Failed to create shooter' });
  }
});

export default router;
```

### Week 4-5: Frontend Foundation
```typescript
// frontend/src/app/layout.tsx
import './globals.css'
import { Inter } from 'next/font/google'
import { Providers } from './providers'

const inter = Inter({ subsets: ['latin'] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen bg-gray-50">
            <nav className="bg-white shadow-sm border-b">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                  <div className="flex items-center">
                    <h1 className="text-xl font-semibold text-gray-900">
                      NRAA Management System
                    </h1>
                  </div>
                </div>
              </div>
            </nav>
            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
              {children}
            </main>
          </div>
        </Providers>
      </body>
    </html>
  )
}
```

```typescript
// frontend/src/app/shooters/page.tsx
'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { ShooterTable } from '@/components/ShooterTable'
import { SearchInput } from '@/components/SearchInput'
import { Pagination } from '@/components/Pagination'

export default function ShootersPage() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['shooters', page, search],
    queryFn: async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '50',
        ...(search && { search })
      })
      
      const response = await fetch(`/api/shooters?${params}`)
      if (!response.ok) throw new Error('Failed to fetch shooters')
      return response.json()
    }
  })
  
  if (error) {
    return <div className="text-red-600">Error loading shooters</div>
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Shooters</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
          Add Shooter
        </button>
      </div>
      
      <SearchInput 
        value={search}
        onChange={setSearch}
        placeholder="Search shooters..."
      />
      
      <ShooterTable 
        shooters={data?.data || []}
        loading={isLoading}
      />
      
      {data?.pagination && (
        <Pagination
          currentPage={page}
          totalPages={data.pagination.pages}
          onPageChange={setPage}
        />
      )}
    </div>
  )
}
```

### Week 5-6: Authentication & Basic UI Components
```typescript
// frontend/src/components/ShooterTable.tsx
import { Shooter } from '@/types'

interface ShooterTableProps {
  shooters: Shooter[]
  loading: boolean
}

export function ShooterTable({ shooters, loading }: ShooterTableProps) {
  if (loading) {
    return <div className="animate-pulse">Loading...</div>
  }
  
  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SID
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Club
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {shooters.map((shooter) => (
            <tr key={shooter.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {shooter.sid}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {shooter.details?.first_name} {shooter.details?.last_name}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {shooter.details?.email}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {shooter.club}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  shooter.status === 'Active' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {shooter.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
```

## Next Steps After Phase 1

1. **Complete shooter management module** with full CRUD operations
2. **Implement event management** with complex scheduling
3. **Build result entry system** with score calculations
4. **Create reporting dashboard** with charts and exports
5. **Add team management** functionality
6. **Implement data migration scripts** for production deployment

This foundation provides a solid base for building out the complete NRAA system with modern technologies while preserving all existing data and functionality.
