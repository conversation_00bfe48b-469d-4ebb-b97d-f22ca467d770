#!/bin/bash

# NRAA OPMP Docker Build Script
# This script builds and pushes images to the local registry at tower.local:5000

set -e

# Configuration
REGISTRY="tower.local:5000"
PROJECT_NAME="nraa-opmp"
VERSION=${1:-latest}

echo "🚀 Building NRAA OPMP Docker images..."
echo "Registry: $REGISTRY"
echo "Version: $VERSION"

# Function to build and push image
build_and_push() {
    local service=$1
    local context=$2
    local image_name="$REGISTRY/$PROJECT_NAME-$service:$VERSION"
    
    echo "📦 Building $service..."
    docker build -t "$image_name" "$context"
    
    echo "📤 Pushing $image_name to registry..."
    docker push "$image_name"
    
    # Also tag as latest if not already latest
    if [ "$VERSION" != "latest" ]; then
        local latest_image="$REGISTRY/$PROJECT_NAME-$service:latest"
        docker tag "$image_name" "$latest_image"
        docker push "$latest_image"
    fi
    
    echo "✅ $service build complete"
}

# Pull base images to local registry (if not already present)
echo "📥 Ensuring base images are available in local registry..."

# Check if base images exist in local registry, if not pull and push them
BASE_IMAGES=("node:18-alpine" "mysql:8.0" "nginx:alpine")

for base_image in "${BASE_IMAGES[@]}"; do
    local_image="$REGISTRY/$base_image"
    
    if ! docker manifest inspect "$local_image" > /dev/null 2>&1; then
        echo "📥 Pulling and pushing $base_image to local registry..."
        docker pull "$base_image"
        docker tag "$base_image" "$local_image"
        docker push "$local_image"
    else
        echo "✅ $base_image already exists in local registry"
    fi
done

# Build backend
build_and_push "backend" "./backend"

# Build frontend
build_and_push "frontend" "./frontend"

echo "🎉 All images built and pushed successfully!"
echo ""
echo "To deploy the application:"
echo "  docker-compose up -d"
echo ""
echo "To view logs:"
echo "  docker-compose logs -f"
echo ""
echo "Images created:"
echo "  - $REGISTRY/$PROJECT_NAME-backend:$VERSION"
echo "  - $REGISTRY/$PROJECT_NAME-frontend:$VERSION"
