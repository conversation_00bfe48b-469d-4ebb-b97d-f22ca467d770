#!/bin/bash

# NRAA OPMP Deployment Script
# This script deploys the application using Docker Compose

set -e

# Configuration
PROJECT_NAME="nraa-opmp"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

echo "🚀 Deploying NRAA OPMP..."

# Check if .env file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your configuration before running again."
        exit 1
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Function to check if service is healthy
check_health() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Checking health of $service..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose ps | grep "$service" | grep -q "healthy"; then
            echo "✅ $service is healthy"
            return 0
        fi
        
        echo "⏳ Waiting for $service to be healthy (attempt $attempt/$max_attempts)..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service failed to become healthy"
    return 1
}

# Stop existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Pull latest images
echo "📥 Pulling latest images..."
docker-compose pull

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to start..."
sleep 10

# Check health of each service
check_health "nraa-opmp-db"
check_health "nraa-opmp-backend"
check_health "nraa-opmp-frontend"

echo "🎉 Deployment completed successfully!"
echo ""
echo "Application URLs:"
echo "  Frontend: http://localhost"
echo "  API:      http://localhost/api"
echo "  Direct Frontend: http://localhost:3000"
echo "  Direct API:      http://localhost:3001"
echo ""
echo "Useful commands:"
echo "  View logs:     docker-compose logs -f"
echo "  Stop services: docker-compose down"
echo "  Restart:       docker-compose restart"
echo "  Status:        docker-compose ps"
