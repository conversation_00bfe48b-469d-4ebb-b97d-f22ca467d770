# NRAA OPMP Environment Configuration

# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/nraa_database"
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=nraa_database
MYSQL_USER=nraa_user
MYSQL_PASSWORD=your_mysql_password

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_API_URL=http://localhost:3001

# Docker Registry
DOCKER_REGISTRY=tower.local:5000

# Optional: SSL Configuration
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/private.key
