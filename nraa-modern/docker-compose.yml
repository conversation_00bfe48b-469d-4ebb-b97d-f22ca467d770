version: '3.8'

services:
  # NRAA OPMP Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: tower.local:5000/nraa-opmp-backend:latest
    container_name: nraa-opmp-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET:-fallback-secret-change-in-production}
    volumes:
      - backend_logs:/app/logs
    networks:
      - nraa-network
    depends_on:
      - db
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # NRAA OPMP Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: tower.local:5000/nraa-opmp-frontend:latest
    container_name: nraa-opmp-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:3001
    networks:
      - nraa-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database (if not using external database)
  db:
    image: tower.local:5000/mysql:8.0
    container_name: nraa-opmp-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/db/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - nraa-network
    command: --default-authentication-plugin=mysql_native_password

  # Nginx Reverse Proxy
  nginx:
    image: tower.local:5000/nginx:alpine
    container_name: nraa-opmp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - nraa-network
    depends_on:
      - frontend
      - backend

volumes:
  mysql_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  nraa-network:
    driver: bridge
