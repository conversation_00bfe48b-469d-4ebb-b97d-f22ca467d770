#!/usr/bin/env bash
# Setup script for NRAA development database
# This script imports the live database dumps for development

set -e

echo "Setting up NRAA development database..."

# Check if Docker is running
if ! docker-compose ps | grep -q "database"; then
  echo "Error: Docker database container is not running."
  echo "Please start the Docker containers with 'docker-compose up -d' first."
  exit 1
fi

# Check if database dumps exist
if [[ ! -f db/nraa_wp.sql.gz ]] || [[ ! -f db/nraa_data.sql.gz ]]; then
  echo "Error: Database dumps not found in the db directory."
  echo "Please ensure nraa_wp.sql.gz and nraa_data.sql.gz are in the db directory."
  exit 1
fi

# Create databases
echo "Creating databases..."
docker-compose exec database sh -c "cat /opt/db/create_databases.sql | mysql -u root -ppassword"

# Import WordPress database
echo "Importing WordPress database (nraa_wp)..."
docker-compose exec database sh -c "zcat /opt/db/nraa_wp.sql.gz | mysql -u root -ppassword nraa_wp"

# Import NRAA data database
echo "Importing NRAA data database (nraa_data)..."
echo "This may take a while for large database dumps..."
docker-compose exec database sh -c "zcat /opt/db/nraa_data.sql.gz | mysql -u root -ppassword nraa_data"

# Apply database fixes
echo "Applying database fixes..."
docker-compose exec wordpress php /opt/tools/fix-database-issues.php

echo "Database setup completed successfully!"
echo "You can now access the application at http://localhost:8000"
echo "Database admin interface is available at http://localhost:8080"
echo ""
echo "If you encounter a 'critical error' message:"
echo "1. Check the WordPress debug log for more information"
echo "2. Run the database check script: docker-compose exec wordpress php /opt/tools/check-database-tables.php"
echo "3. Try running the fix script again: docker-compose exec wordpress php /opt/tools/fix-database-issues.php"
