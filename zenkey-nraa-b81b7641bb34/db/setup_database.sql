-- Setup script for NRAA database
-- Compatible with MySQL 5.6

-- Create the databases if they don't exist
CREATE DATABASE IF NOT EXISTS `nraa_wp`;
CREATE DATABASE IF NOT EXISTS `nraa_data`;

-- Create a user for the application with appropriate permissions
-- Using older MySQL 5.6 compatible syntax
-- First check if the user exists
SELECT IF(EXISTS(SELECT 1 FROM mysql.user WHERE user = 'nraa_user' AND host = '%'),
    'User nraa_user@% already exists, skipping creation',
    'Creating user nraa_user@%') AS message;

-- Only create the user if it doesn't exist
-- This is done in a way that's compatible with MySQL 5.6
SET @create_user = CONCAT("CREATE USER 'nraa_user'@'%' IDENTIFIED BY '", 'nraa_password', "'");
SET @user_exists = EXISTS(SELECT 1 FROM mysql.user WHERE user = 'nraa_user' AND host = '%');
SET @sql = IF(@user_exists = 0, @create_user, 'SELECT "User already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Grant privileges (this is safe to run even if the user already exists)
GRANT ALL PRIVILEGES ON `nraa_wp`.* TO 'nraa_user'@'%';
GRANT ALL PRIVILEGES ON `nraa_data`.* TO 'nraa_user'@'%';
FLUSH PRIVILEGES;

-- Switch to the nraa_data database for schema creation
USE `nraa_data`;

-- Create tables for the nraa_data database
-- Associations table
CREATE TABLE IF NOT EXISTS `associations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` enum('ACT','NSW','VIC','QLD','SA','WA','TAS','NT','NQLD','INTERNAT','NRAA') NOT NULL,
  `code` int(11) NOT NULL,
  `start_range` int(11) NOT NULL,
  `end_range` int(11) NOT NULL,
  `next_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Disciplines table
CREATE TABLE IF NOT EXISTS `disciplines` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `short_name` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Grades table
CREATE TABLE IF NOT EXISTS `grades` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `discipline_id` int(10) unsigned NOT NULL,
  `name` varchar(50) NOT NULL,
  `threshold` float NOT NULL,
  `order` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `discipline_id` (`discipline_id`),
  CONSTRAINT `grades_ibfk_1` FOREIGN KEY (`discipline_id`) REFERENCES `disciplines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Divisions table
CREATE TABLE IF NOT EXISTS `divisions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `short_name` varchar(50) NOT NULL,
  `long_name` varchar(255) NOT NULL,
  `order` int(11) NOT NULL,
  `is_open` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Shooters table
CREATE TABLE IF NOT EXISTS `shooters` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sid` varchar(50) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sid` (`sid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Shooter details table
CREATE TABLE IF NOT EXISTS `shooter_details` (
  `shooter_id` int(10) unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) NOT NULL,
  `preferred_name` varchar(255) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `date_of_birth` int(11) DEFAULT NULL,
  `home_phone` varchar(255) DEFAULT NULL,
  `mobile_phone` varchar(255) DEFAULT NULL,
  `is_right_handed` tinyint(1) NOT NULL DEFAULT '1',
  `is_using_bench` tinyint(1) NOT NULL DEFAULT '0',
  `is_coach` tinyint(1) NOT NULL DEFAULT '0',
  `is_competition_coach` tinyint(1) NOT NULL DEFAULT '0',
  `special_flags` smallint(5) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`shooter_id`),
  CONSTRAINT `shooter_details_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Shooter addresses table
CREATE TABLE IF NOT EXISTS `shooter_addresses` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `shooter_id` int(10) unsigned NOT NULL,
  `type` enum('primary','mailing') NOT NULL DEFAULT 'primary',
  `line_1` varchar(255) NOT NULL,
  `line_2` varchar(255) DEFAULT NULL,
  `line_3` varchar(255) DEFAULT NULL,
  `suburb` varchar(255) NOT NULL,
  `state` enum('ACT','NSW','VIC','QLD','SA','WA','TAS','NT') NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shooter_id` (`shooter_id`),
  CONSTRAINT `shooter_addresses_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Shooter membership table
CREATE TABLE IF NOT EXISTS `shooter_membership` (
  `shooter_id` int(10) unsigned NOT NULL,
  `association_id` int(10) unsigned NOT NULL,
  `membership_no` varchar(255) DEFAULT NULL,
  `joined_date` int(11) NOT NULL,
  `expiry_date` int(11) NOT NULL,
  `license_number` varchar(255) DEFAULT NULL,
  `license_expiry_date` int(11) DEFAULT NULL,
  `has_atr_magazine` tinyint(1) NOT NULL DEFAULT '0',
  `notes` text,
  PRIMARY KEY (`shooter_id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `shooter_membership_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `shooter_membership_ibfk_2` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Shooter grade table
CREATE TABLE IF NOT EXISTS `shooter_grade` (
  `shooter_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`shooter_id`,`grade_id`),
  KEY `grade_id` (`grade_id`),
  CONSTRAINT `shooter_grade_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `shooter_grade_ibfk_2` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Events table
CREATE TABLE IF NOT EXISTS `events` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `start_date` int(11) NOT NULL,
  `end_date` int(11) NOT NULL,
  `location` varchar(255) NOT NULL,
  `association_id` int(10) unsigned DEFAULT NULL,
  `is_competition` tinyint(1) NOT NULL DEFAULT '1',
  `is_team_event` tinyint(1) NOT NULL DEFAULT '0',
  `is_queens_event` tinyint(1) NOT NULL DEFAULT '0',
  `is_ranked` tinyint(1) NOT NULL DEFAULT '0',
  `is_enter_shots` tinyint(1) NOT NULL,
  `is_divisional` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `events_ibfk_1` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Ranges table
CREATE TABLE IF NOT EXISTS `ranges` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `date` int(11) NOT NULL,
  `distance` enum('300y','400y','500y','600y','700y','800y','900y','1000y','300m','400m','500m','600m','700m','800m','900m','agg') NOT NULL,
  `number_of_shots` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Event_Range table (junction table)
CREATE TABLE IF NOT EXISTS `event_range` (
  `event_id` int(10) unsigned NOT NULL,
  `range_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`event_id`,`range_id`),
  KEY `range_id` (`range_id`),
  CONSTRAINT `event_range_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_range_ibfk_2` FOREIGN KEY (`range_id`) REFERENCES `ranges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Subevents table
CREATE TABLE IF NOT EXISTS `subevents` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_enter_shots` tinyint(1) NOT NULL DEFAULT '0',
  `start_date` int(11) NOT NULL,
  `end_date` int(11) NOT NULL,
  `special_rules` enum('KALTENBERG','MACE') DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  CONSTRAINT `subevents_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
