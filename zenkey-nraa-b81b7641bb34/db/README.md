# Database Files

This directory contains the database dumps used for setting up the development environment.

## Files

- `nraa_wp.sql.gz`: WordPress database dump
- `nraa_data.sql.gz`: NRAA application data database dump
- `create_databases.sql`: SQL script to create the databases and set up users
- `setup_database.sql`: SQL script with database schema (used as a fallback if dumps are not available)

## Usage

These files are used by the `tools/setup-dev-database.sh` script to set up the development environment.

To set up the database:

1. Ensure Docker is running
2. Run `./tools/setup-dev-database.sh`

## Updating Database Dumps

To update the database dumps with fresh data from production:

1. Export the databases from the production server:
   ```
   mysqldump -u [username] -p [password] nraa_wp | gzip > nraa_wp.sql.gz
   mysqldump -u [username] -p [password] nraa_data | gzip > nraa_data.sql.gz
   ```

2. Copy the dumps to this directory
3. Run the setup script to import the new dumps
