-- Create the DBs
CREATE DATABASE IF NOT EXISTS `nraa_wp`;
CREATE DATABASE IF NOT EXISTS `nraa_data`;

-- Update the root user to accept connections from anywhere
-- Using MySQL 5.6 compatible syntax
-- First check if the user exists
SELECT IF(EXISTS(SELECT 1 FROM mysql.user WHERE user = 'root' AND host = '%'),
    'User root@% already exists, skipping creation',
    'Creating user root@%') AS message;

-- Only create the user if it doesn't exist
-- This is done in a way that's compatible with MySQL 5.6
SET @create_user = CONCAT("CREATE USER 'root'@'%' IDENTIFIED BY '", 'password', "'");
SET @user_exists = EXISTS(SELECT 1 FROM mysql.user WHERE user = 'root' AND host = '%');
SET @sql = IF(@user_exists = 0, @create_user, 'SELECT "User already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Grant privileges (this is safe to run even if the user already exists)
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
