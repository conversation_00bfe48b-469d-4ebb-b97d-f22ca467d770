CREATE TABLE `team_results` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `match_id` int(10) unsigned NOT NULL,
  `team_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  `place` int(11) NOT NULL,
  `shots` varchar(255) DEFAULT NULL,
  `score_whole` int(11) NOT NULL,
  `score_partial` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `match_id` (`match_id`),
  KEY `team_id` (`team_id`),
  KEY `grade_id` (`grade_id`),
  CONSTRAINT `team_results_ibfk_1` FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `team_results_ibfk_4` FOREIGN KEY (`team_id`) REFERENCES `event_teams` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `team_results_ibfk_5` FOR<PERSON><PERSON><PERSON> KEY (`grade_id`) REFERENCES `grades` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1