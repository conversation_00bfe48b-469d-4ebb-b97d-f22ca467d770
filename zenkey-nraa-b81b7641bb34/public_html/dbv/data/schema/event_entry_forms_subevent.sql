CREATE TABLE `event_entry_forms_subevent` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_entry_form_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique` (`event_entry_form_id`,`subevent_id`,`grade_id`),
  KEY `subevent_fk` (`subevent_id`),
  KEY `event_entry_form_fk` (`event_entry_form_id`),
  KEY `grade_fk` (`grade_id`),
  CONSTRAINT `event_entry_forms_subevent_ibfk_1` FOREIGN KEY (`event_entry_form_id`) REFERENCES `event_entry_forms` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_entry_forms_subevent_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_entry_forms_subevent_ibfk_4` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1