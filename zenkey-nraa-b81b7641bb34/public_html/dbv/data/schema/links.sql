CREATE TABLE `links` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `link_section_id` int(10) unsigned NOT NULL,
  `label` varchar(255) NOT NULL,
  `phone` varchar(128) NOT NULL,
  `url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `link_section_id` (`link_section_id`),
  CONSTRAINT `links_ibfk_2` FOREIGN KEY (`link_section_id`) REFERENCES `link_sections` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1