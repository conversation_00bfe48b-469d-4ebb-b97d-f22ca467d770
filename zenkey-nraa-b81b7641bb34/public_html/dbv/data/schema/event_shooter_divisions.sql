CREATE TABLE `event_shooter_divisions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `division_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON>EY `event_id` (`event_id`),
  KEY `subevent_id` (`subevent_id`),
  KEY `shooter_id` (`shooter_id`),
  KEY `division_id` (`division_id`),
  CONSTRAINT `event_shooter_divisions_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_shooter_divisions_ibfk_2` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_shooter_divisions_ibfk_3` FOR<PERSON><PERSON><PERSON> KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_shooter_divisions_ibfk_4` FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1