CREATE TABLE `shooter_update_requests` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sid` int(10) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `preferred_name` varchar(255) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `is_right_handed` tinyint(1) NOT NULL DEFAULT '1',
  `is_using_bench` tinyint(1) NOT NULL DEFAULT '0',
  `is_coach` tinyint(1) NOT NULL DEFAULT '0',
  `is_competition_coach` tinyint(1) NOT NULL DEFAULT '0',
  `state` enum('ACT','NSW','VIC','QLD','SA','WA','TAS','NT') NOT NULL DEFAULT 'QLD',
  `club` varchar(255) DEFAULT NULL,
  `membership_no` varchar(255) DEFAULT NULL,
  `reason` text,
  `type` enum('new','change') NOT NULL DEFAULT 'new',
  `created_date` int(10) NOT NULL,
  `line_1` varchar(255) DEFAULT NULL,
  `line_2` varchar(255) DEFAULT NULL,
  `line_3` varchar(255) DEFAULT NULL,
  `suburb` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `home_phone` varchar(255) DEFAULT NULL,
  `mobile_phone` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `association_id` int(10) unsigned DEFAULT NULL,
  `date_of_birth` int(11) DEFAULT NULL,
  `license_number` varchar(255) DEFAULT NULL,
  `license_expiry_date` int(11) DEFAULT NULL,
  `notes` text,
  PRIMARY KEY (`id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `shooter_update_requests_ibfk_2` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='Temporary storage for user submitted data'