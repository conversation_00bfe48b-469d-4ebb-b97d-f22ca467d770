CREATE FUNCTION `GetMatchCount`(`matchId` int(10) unsigned) RETURNS int(10) unsigned
BEGIN
    SELECT COUNT(*) INTO @matchCount
      FROM (
           SELECT DISTINCT m2.id
             FROM match_ranges mr1
             JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
             JOIN matches m1 ON m1.id = mr1.match_id
             JOIN matches m2 ON m2.id = mr2.match_id
            WHERE m1.id = matchId
              AND m1.number > m2.number
              AND m2.is_cancelled = 0
         GROUP BY mr2.range_id
     ) t;
  RETURN @matchCount;
END