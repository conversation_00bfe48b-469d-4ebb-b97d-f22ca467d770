CREATE TABLE `shooter_accreditations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `shooter_id` int(10) unsigned NOT NULL,
  `type` enum('Range','Butts') NOT NULL,
  `expiry_date` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `shooter_id` (`shooter_id`),
  CONSTRAINT `shooter_accreditations_ibfk_1` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1