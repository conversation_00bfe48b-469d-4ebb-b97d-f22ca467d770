CREATE TABLE `event_teams_shooters` (
  `team_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `is_captain` tinyint(1) NOT NULL,
  `is_coach` tinyint(1) NOT NULL,
  KEY `team_id` (`team_id`),
  <PERSON><PERSON><PERSON> `shooter` (`shooter_id`),
  CONSTRAINT `event_teams_shooters_ibfk_3` FOREIGN KEY (`team_id`) REFERENCES `event_teams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `event_teams_shooters_ibfk_4` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1