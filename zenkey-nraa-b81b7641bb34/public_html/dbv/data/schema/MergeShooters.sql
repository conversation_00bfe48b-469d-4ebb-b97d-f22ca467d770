CREATE PROCEDURE `MergeShooters`(IN `fromSid` int(10) unsigned, IN `toSid` int(10) unsigned)
BEGIN
START TRANSACTION;
-- Need their IDs
SELECT id INTO @fromId FROM shooters WHERE sid = fromSid;
SELECT id INTO @toId FROM shooters WHERE sid = toSid;

-- Shooter details
-- TODO: Possibly update their details but its hard to say if this is viable
UPDATE shooter_secondary_memberships SET shooter_id = @toId WHERE shooter_id = @fromId;

-- Event stuff
UPDATE event_teams_shooters SET shooter_id = @toId WHERE shooter_id = @fromId;
UPDATE event_entry_forms SET shooter_id = @toId WHERE shooter_id = @fromId;
UPDATE event_shooter_divisions SET shooter_id = @toId WHERE shooter_id = @fromId;

-- Results
UPDATE results SET shooter_id = @toId WHERE shooter_id = @fromId;

-- Audit Trails
UPDATE audit_trails SET row_id = @toId WHERE row_id = @fromId AND table_name = "shooters";
UPDATE audit_trails SET row_id = @toId WHERE row_id = @fromId AND table_name LIKE "shooter_%";
UPDATE audit_trail_changes SET `to` = @toId WHERE `to` = @fromId AND column_name = "shooter_id";

-- Cleanup
DELETE FROM shooters WHERE id = @fromId AND sid = fromSid LIMIT 1;

COMMIT;
END