CREATE TABLE `shooter_secondary_memberships` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `association_id` int(10) unsigned NOT NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `joined_date` int(11) NOT NULL,
  `expiry_date` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `shooter_id` (`shooter_id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `shooter_secondary_memberships_ibfk_1` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `shooter_secondary_memberships_ibfk_2` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1