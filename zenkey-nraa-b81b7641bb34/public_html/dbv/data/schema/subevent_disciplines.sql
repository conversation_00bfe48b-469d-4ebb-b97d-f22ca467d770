CREATE TABLE `subevent_disciplines` (
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `discipline_id` int(10) unsigned DEFAULT NULL,
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `subevent_id` (`subevent_id`,`discipline_id`),
  <PERSON><PERSON>Y `discipline_id` (`discipline_id`),
  CONSTRAINT `subevent_disciplines_ibfk_1` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `subevent_disciplines_ibfk_2` FOREIGN KEY (`discipline_id`) REFERENCES `disciplines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1