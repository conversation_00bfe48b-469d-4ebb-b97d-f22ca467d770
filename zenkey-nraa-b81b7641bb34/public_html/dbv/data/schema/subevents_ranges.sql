CREATE TABLE `subevents_ranges` (
  `subevent_id` int(10) unsigned NOT NULL,
  `range_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `range_id` (`range_id`),
  KEY `subevent_id` (`subevent_id`),
  CONSTRAINT `subevents_ranges_ibfk_8` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `subevents_ranges_ibfk_9` FOREIGN KEY (`range_id`) REFERENCES `ranges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1