CREATE TABLE `shooters_grades` (
  `shooter_id` int(10) unsigned NOT NULL,
  `grade_id` int(10) unsigned NOT NULL,
  UNIQUE KEY `unique_only` (`shooter_id`,`grade_id`),
  KEY `shooter_id` (`shooter_id`),
  KEY `grade_id` (`grade_id`),
  CONSTRAINT `shooters_grades_ibfk_3` FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `shooters_grades_ibfk_4` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1