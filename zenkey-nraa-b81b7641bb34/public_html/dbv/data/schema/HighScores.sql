CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `HighScores` AS select `g`.`discipline_id` AS `discipline_id`,`r`.`match_id` AS `match_id`,max(`CombineScore`(`r`.`score_whole`,`r`.`score_partial`)) AS `Score` from (((`results` `r` join `grades` `g` on((`g`.`id` = `r`.`grade_id`))) join `matches` `m` on((`m`.`id` = `r`.`match_id`))) join `events` `e` on((`e`.`id` = `m`.`event_id`))) where ((`m`.`is_graded` = 1) and (`e`.`is_team_event` = 0) and (`e`.`end_date` >= `GetStartDate`())) group by `m`.`id`,`g`.`discipline_id`