CREATE TABLE `event_targets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `event_squads_setting_id` int(10) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `order` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `event_squads_setting_id` (`event_squads_setting_id`),
  CONSTRAINT `event_targets_ibfk` FOREIGN KEY (`event_squads_setting_id`) REFERENCES `event_squads_settings` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1