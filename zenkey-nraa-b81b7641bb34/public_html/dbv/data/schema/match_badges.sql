CREATE TABLE `match_badges` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `grade_id` int(10) unsigned DEFAULT NULL,
  `match_id` int(11) unsigned NOT NULL,
  `number_of_badges` int(11) DEFAULT NULL,
  `subevent_id` int(10) unsigned DEFAULT NULL,
  `division_id` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `match_id` (`match_id`),
  KEY `grade_id` (`grade_id`),
  KEY `subevent_id` (`subevent_id`),
  KEY `division_id` (`division_id`),
  CONSTRAINT `match_badges_ibfk_1` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_2` FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_3` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_4` FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_5` FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_6` FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `match_badges_ibfk_7` FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1