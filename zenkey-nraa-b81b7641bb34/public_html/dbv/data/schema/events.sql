CREATE TABLE `events` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `start_date` int(11) NOT NULL,
  `end_date` int(11) NOT NULL,
  `location` varchar(255) NOT NULL,
  `association_id` int(10) unsigned DEFAULT NULL,
  `is_competition` tinyint(1) NOT NULL DEFAULT '1',
  `is_team_event` tinyint(1) NOT NULL DEFAULT '0',
  `is_queens_event` tinyint(1) NOT NULL DEFAULT '0',
  `is_ranked` tinyint(1) NOT NULL DEFAULT '0',
  `is_enter_shots` tinyint(1) NOT NULL,
  `is_divisional` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `association_id` (`association_id`),
  CONSTRAINT `events_ibfk_2` FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1