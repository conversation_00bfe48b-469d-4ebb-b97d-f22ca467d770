CREATE EVENT grading_algorithm_event
  ON SCHEDULE EVERY 24 HOUR STARTS '2014-03-04 23:19:20'
  DO BEGIN
    SET @num := 0, @type := 0;

    DELETE FROM shooter_calculated_grades;

    INSERT INTO shooter_calculated_grades (shooter_id, score, grade, discipline_id, discipline_name)
    -- Why are we saving the discipline name in this table? is there a reason for this de-normalization?
    -- Why was the GetGrade function using hardcoded values instead of the threshold values in the grades table?
    SELECT s.id, SUM(grd.pctscore) / COUNT(*) AS Score, GetGrade(SUM(grd.pctscore) / COUNT(*), d.id) AS Grade, d.id, d.name AS discipline
    FROM results r
    JOIN grades g       ON r.grade_id = g.id
    JOIN shooters s     ON r.shooter_id = s.id
    JOIN matches m      ON r.match_id = m.id
    JOIN events e       ON m.event_id = e.id
    JOIN disciplines d  ON discipline_id = d.id
    JOIN (
      -- Why? Why cast them to strings and concatenate? these are not decimal values and cannot be compared as such
      SELECT DISTINCT r.shooter_id, s.sid, discipline_id,
        DATE_FORMAT(FROM_UNIXTIME(e.end_date), '%e %b %Y') as EventEndDate,
        r.match_id,
        CAST(CONCAT(CAST(score_whole AS CHAR(50)), '.', CAST(score_partial AS CHAR(50))) AS DECIMAL(10,3)) AS Score,
        ts.TopScore,
        CAST(CONCAT(CAST(score_whole AS CHAR(50)), '.', CAST(score_partial AS CHAR(50))) AS DECIMAL(10,3)) / ts.topscore * 100 AS PctScore,
        g.name AS grade,
        d.name AS discipline,
        @num := IF(@type = shooter_id, @num + 1, 1) AS row_number, -- I don't understand why this is being done?
        @type := shooter_id AS dummy
      FROM (SELECT * FROM results ORDER BY shooter_id, id DESC) r
      JOIN grades g       ON r.grade_id = g.id
      JOIN shooters s     ON r.shooter_id = s.id
      JOIN matches m      ON r.match_id = m.id
      JOIN events e       ON m.event_id = e.id
      JOIN disciplines d  ON discipline_id = d.id
      JOIN (
        SELECT match_id, grade_id, MAX(CAST(CONCAT(CAST(score_whole AS CHAR(50)), '.', CAST(score_partial AS CHAR(50))) AS DECIMAL(10,3))) AS TopScore
        FROM results r
        JOIN grades g   ON r.grade_id = g.id
        JOIN matches m  ON r.match_id = m.id
        JOIN events e   ON m.event_id = e.id
        WHERE m.is_graded = 1
          AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
          AND e.is_team_event = 0
        GROUP BY match_id, grade_id
        ORDER BY r.shooter_id
      ) ts ON (r.match_id = ts.match_id AND ts.grade_id = r.grade_id)
      WHERE m.is_graded = 1
        AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
        AND e.is_team_event = 0
      ORDER BY r.shooter_id
    ) grd ON (grd.shooter_id = s.id AND grd.discipline_id = d.id)
    WHERE row_number <= 8
    GROUP BY d.id, s.id
    ORDER BY s.id;

    UPDATE shooter_calculated_grades scg
    SET number_of_shoots = (
      SELECT COUNT(*)
      FROM (SELECT * FROM results ORDER BY shooter_id, id DESC) r
      JOIN grades g       ON r.grade_id = g.id
      JOIN shooters s     ON r.shooter_id = s.id
      JOIN matches m      ON r.match_id = m.id
      JOIN events e       ON m.event_id = e.id
      JOIN disciplines d  ON discipline_id = d.id
      WHERE m.is_graded = 1
        AND FROM_UNIXTIME(e.end_date) >= DATE_SUB(NOW(), INTERVAL 2 YEAR)
        AND e.is_team_event = 0
        AND s.id = scg.shooter_id
        AND d.id = scg.discipline_id
      GROUP BY s.id, discipline_id
    );
END;
