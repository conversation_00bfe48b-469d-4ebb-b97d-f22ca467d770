SET NAMES utf8;
SET time_zone = '+10:00';

DELIMITER ;;

DROP PROCEDURE IF EXISTS `CalculateDivisionalAggregateMatchForCups`;;
CREATE PROCEDURE `CalculateDivisionalAggregateMatchForCups` (IN `matchId` int(10) unsigned, IN `allowPartialParticipation` tinyint(1) unsigned)
BEGIN
    DELETE FROM results WHERE match_id = matchId;

    INSERT INTO results (match_id, shooter_id, grade_id, place, shots, score_whole, score_partial)
      SELECT
        matchId,
        shooter_id,
        grade_id,
        0,
        shots,
        score_whole,
        score_partial
      FROM (
          SELECT
              r.shooter_id,
              r.grade_id,
              GROUP_CONCAT(r.shots SEPARATOR '') AS shots,
              SUM(r.score_whole)                 AS score_whole,
              SUM(r.score_partial)               AS score_partial
            FROM results r
      INNER JOIN (   SELECT DISTINCT m2.id AS match_id
                       FROM match_ranges mr1
                       JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
                       JOIN matches m1 ON m1.id = mr1.match_id
                       JOIN matches m2 ON m2.id = mr2.match_id
                      WHERE m1.id = matchId
                        AND m1.number > m2.number
                        AND m2.is_cancelled = 0
                        AND IsAggregate(m2.id) = 0
                   GROUP BY mr2.range_id
                 ) mids ON mids.match_id = r.match_id
            JOIN matches m ON m.id = r.match_id
            JOIN match_ranges mr ON mr.match_id = m.id
            JOIN subevents_ranges sr ON sr.range_id = mr.range_id
            JOIN subevents se ON se.id = sr.subevent_id
            JOIN event_shooter_divisions esd ON esd.shooter_id = r.shooter_id
                                            AND esd.event_id = m.event_id
                                            AND (esd.subevent_id = NULL OR esd.subevent_id = se.id)
           WHERE r.score_whole > 0
        GROUP BY r.shooter_id, esd.division_id
          HAVING allowPartialParticipation = 1 OR COUNT(r.id) >= GetMatchCount(matchId)
      ) t;
  END;;

DROP PROCEDURE IF EXISTS `CalculateDivisionalAggregateMatchForNoCups`;;
CREATE PROCEDURE `CalculateDivisionalAggregateMatchForNoCups` (IN `matchId` int(10) unsigned, IN `allowPartialParticipation` tinyint(1) unsigned)
BEGIN
    DELETE FROM results WHERE match_id = matchId;

    INSERT INTO results (match_id, shooter_id, grade_id, place, shots, score_whole, score_partial)
      SELECT
        matchId,
        shooter_id,
        grade_id,
        0,
        shots,
        score_whole,
        score_partial
      FROM (
          SELECT
              r.shooter_id,
              r.grade_id,
              GROUP_CONCAT(r.shots SEPARATOR '') AS shots,
              SUM(r.score_whole)                 AS score_whole,
              SUM(r.score_partial)               AS score_partial
            FROM results r
      INNER JOIN (   SELECT DISTINCT m2.id AS match_id
                       FROM match_ranges mr1
                       JOIN match_ranges mr2 ON mr1.range_id = mr2.range_id
                       JOIN matches m1 ON m1.id = mr1.match_id
                       JOIN matches m2 ON m2.id = mr2.match_id
                      WHERE m1.id = matchId
                        AND m1.number > m2.number
                        AND m2.is_cancelled = 0
                        AND IsAggregate(m2.id) = 0
                   GROUP BY mr2.range_id
                 ) mids ON mids.match_id = r.match_id
            JOIN matches m ON m.id = r.match_id
            JOIN match_ranges mr ON mr.match_id = m.id
            JOIN event_shooter_divisions esd ON esd.shooter_id = r.shooter_id
                                            AND esd.event_id = m.event_id
        GROUP BY r.shooter_id, esd.division_id
          HAVING allowPartialParticipation = 1 OR COUNT(r.id) >= GetMatchCount(matchId)
      ) t;
  END;;

DELIMITER ;

-- 2018-06-24
