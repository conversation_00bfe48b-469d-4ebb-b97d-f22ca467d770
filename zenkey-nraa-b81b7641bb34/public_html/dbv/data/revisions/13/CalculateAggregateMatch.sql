DROP PROCEDURE IF EXISTS `CalculateAggregateMatch`;
CREATE PROCEDURE `CalculateAggregateMatch` (IN `matchId` int(11) unsigned)
  BEGIN
    DELETE FROM `results` WHERE `match_id` = matchId;

    INSERT INTO `results` (`match_id`, `shooter_id`, `grade_id`, `place`, `shots`, `score_whole`, `score_partial`)
      SELECT matchId, `shooter_id`, `grade_id`, 0, `shots`, `score_whole`, `score_partial`
      FROM (
        SELECT r.`shooter_id`, r.`grade_id`, GROUP_CONCAT(r.`shots` SEPARATOR '') as `shots`, SUM(r.`score_whole`) as `score_whole`, SUM(r.`score_partial`) as `score_partial`, COUNT(*) as `count`
        FROM `results` r
        WHERE r.`score_whole` > 0 AND r.`match_id` IN (
          SELECT mr1.`match_id`
          FROM `match_ranges` mr1
          JOIN `match_ranges` mr2 ON mr1.`range_id` = mr2.`range_id`
          WHERE mr2.`match_id` = matchId
          GROUP BY mr1.`match_id`
          HAVING COUNT(*) = 1
        )
        GROUP BY r.`shooter_id`, r.`grade_id`
        HAVING MAX(`count`) = `count`
      ) t;
  END;
