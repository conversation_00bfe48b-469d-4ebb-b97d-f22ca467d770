DROP FUNCTION IF EXISTS GetStartDate;
CREATE FUNCTION GetStartDate() RETURNS INT(11) NOT DETERMINISTIC
  RETURN UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 YEAR));

DROP VIEW IF EXISTS GradedResults;
CREATE VIEW GradedResults AS
  SELECT
    r.shooter_id,
    r.grade_id,
    r.match_id,
    r.score_whole,
    r.score_partial,
    g.discipline_id
  FROM results r
    JOIN matches m ON m.id = r.match_id
    JOIN events e ON e.id = m.event_id
    JOIN grades g ON g.id = r.grade_id
  WHERE m.is_graded = 1
    AND e.is_team_event = 0
    AND e.end_date >= GetStartDate()
    AND r.score_whole > 0
  ORDER BY r.shooter_id, e.end_date DESC, m.number DESC;

DROP VIEW IF EXISTS HighScores;
CREATE VIEW HighScores AS
  SELECT
    g.discipline_id,
    r.match_id,
    MAX(CombineScore(r.score_whole, r.score_partial)) AS Score
  FROM results r
    JOIN grades g ON g.id = r.grade_id
    JOIN matches m ON m.id = r.match_id
    JOIN events e ON e.id = m.event_id
  WHERE m.is_graded = 1
    AND e.is_team_event = 0
    AND e.end_date >= GetStartDate()
  GROUP BY m.id, g.discipline_id;

DROP VIEW IF EXISTS NumberOfShoots;
CREATE VIEW NumberOfShoots AS
  SELECT
    r.shooter_id,
    g.discipline_id,
    IF(COUNT(*) < 8, COUNT(*), 8) AS shoots
  FROM results r
    JOIN grades g ON r.grade_id = g.id
    JOIN matches m ON r.match_id = m.id
    JOIN events e ON m.event_id = e.id
  WHERE m.is_graded = 1
    AND e.is_team_event = 0
    AND e.end_date >= GetStartDate()
  GROUP BY r.shooter_id, g.discipline_id;

DROP VIEW IF EXISTS AuditsOfCalculatedGradesThatHaveNotChanged;
CREATE VIEW AuditsOfCalculatedGradesThatHaveNotChanged AS
  SELECT
    a.id AS `id`,
    MAX(a.timestamp) AS `timestamp`
  FROM shooter_calculated_grades scg
    JOIN grades g ON g.id = scg.grade_id
    JOIN audit_trails a ON a.row_id = scg.id AND a.table_name = 'shooter_calculated_grades'
    JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
  WHERE (ac.column_name = 'shooter_id' AND ac.to = scg.shooter_id) OR
        (ac.column_name = 'grade_id' AND ac.to = scg.grade_id) OR
        (ac.column_name = 'avg_score' AND ac.to = scg.avg_score) OR
        (ac.column_name = 'number_of_shoots' AND ac.to = scg.number_of_shoots)
  GROUP BY scg.shooter_id, g.discipline_id;

DROP PROCEDURE IF EXISTS InsertAuditsOfCalculatedGradesThatHaveChanged;
CREATE PROCEDURE InsertAuditsOfCalculatedGradesThatHaveChanged(IN currentTime INT)
  BEGIN
    INSERT INTO audit_trails (`user_id`, `table_name`, `row_id`, `type`, `timestamp`)
    SELECT
      -1 AS `user_id`,
      'shooter_calculated_grades' AS `table_name`,
      scg.id AS `row_id`,
      IF(MAX(a.timestamp) IS NULL, 2, 3) AS `type`,
      currentTime AS `timestamp`
    FROM shooter_calculated_grades scg
      JOIN grades g ON g.id = scg.grade_id
      LEFT OUTER JOIN audit_trails a ON a.row_id = scg.id
      LEFT OUTER JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
    WHERE a.id IS NULL
      OR (a.table_name = 'shooter_calculated_grades' AND
          ac.column_name = 'avg_score' AND
          ac.to <> scg.avg_score)
    GROUP BY scg.shooter_id, g.discipline_id
    ORDER BY a.timestamp DESC;
  END;

DROP PROCEDURE IF EXISTS InsertAuditChangesOfCalculatedGradeUpdates;
CREATE PROCEDURE InsertAuditChangesOfCalculatedGradeUpdates(IN currentTime INT)
  BEGIN
    INSERT INTO audit_trail_changes (`audit_trail_id`, `column_name`, `from`, `to`)
      SELECT
        a2.id AS `audit_trail_id`,
        ac.column_name AS `column_name`,
        ac.to AS `to`,
        CASE ac.column_name
          WHEN 'grade_id' THEN scg.grade_id
          WHEN 'avg_score' THEN scg.avg_score
          WHEN 'number_of_shoots' THEN scg.number_of_shoots
        END AS `from`
      FROM shooter_calculated_grades scg
        JOIN audit_trails a ON a.row_id = scg.id
        JOIN audit_trail_changes ac ON ac.audit_trail_id = a.id
        JOIN audit_trails a2
          ON a2.timestamp = currentTime AND a2.user_id = 1 AND a2.table_name = 'shooter_calculated_grades' AND a2.type = 3
      WHERE a.table_name = 'shooter_calculated_grades'
        AND (
          (ac.column_name = 'grade_id' AND ac.to <> scg.grade_id) OR
          (ac.column_name = 'avg_score' AND ac.to <> scg.avg_score) OR
          (ac.column_name = 'number_of_shoots' AND ac.to <> scg.number_of_shoots)
        )
      GROUP BY ac.column_name
      ORDER BY a.timestamp DESC;
  END;

DROP PROCEDURE IF EXISTS InsertAuditCalculatedGradeChangesForColumn;
CREATE PROCEDURE InsertAuditCalculatedGradeChangesForColumn(IN columnName VARCHAR(255), IN currentTime INT)
  BEGIN
    SET @query = CONCAT(
      'INSERT INTO `audit_trail_changes` (`audit_trail_id`, `column_name`, `from`, `to`)
        SELECT
          a.id AS `audit_trail_id`,
          ? AS `column_name`,
          NULL AS `from`,
          scg.', columnName, ' AS `to`
      FROM audit_trails a
        JOIN shooter_calculated_grades scg ON scg.id = a.row_id
      WHERE a.row_id = scg.id
            AND a.table_name = \'shooter_calculated_grades\'
            AND a.type = 2
            AND a.timestamp = ?
            AND a.user_id = 1
            AND NOT EXISTS(
        SELECT ac.`audit_trail_id`, ac.`column_name`, ac.`from`, ac.`to`
        FROM audit_trail_changes ac
        WHERE ac.audit_trail_id = a.id AND ac.column_name = ?
      );');
    PREPARE stmt FROM @query;
    EXECUTE stmt USING columnName, currentTime, columnName;
    DEALLOCATE PREPARE stmt;
  END;

DROP PROCEDURE IF EXISTS `GradeShooters`;
CREATE PROCEDURE `GradeShooters`()
  BEGIN
-- Need these to count number of shoots and calculate grade
    SET @sid := 0,
        @row := 0,
        @time := UNIX_TIMESTAMP(NOW()),
        @start_date := GetStartDate();

-- I suppose its easier to clear and replace all grades each time than to prune ones that have fallen out of validity
    TRUNCATE TABLE shooter_calculated_grades;

-- Recalculate every shooters grade
    INSERT INTO shooter_calculated_grades (shooter_id, avg_score, grade_id)
      SELECT
        s.id,
        AVG(Graded.Score / HighScores.Score * 100) AS avg_score,
        GetGrade(AVG(Graded.Score / HighScores.Score * 100), Graded.discipline_id)
      FROM shooters s
        JOIN (
          SELECT
            r.shooter_id,
            r.discipline_id,
            r.match_id,
            CombineScore(r.score_whole, r.score_partial) AS Score,
            @row := IF(@sid = r.shooter_id, @row + 1, 1) AS row_number,
            @sid := r.shooter_id                         AS unused
          FROM GradedResults r
          GROUP BY r.shooter_id, r.match_id, r.discipline_id
        ) Graded ON Graded.shooter_id = s.id
        JOIN HighScores ON HighScores.discipline_id = Graded.discipline_id AND HighScores.match_id = Graded.match_id
      WHERE Graded.row_number <= 8
      GROUP BY s.id, Graded.discipline_id;

-- Update these to add in number of shoots: Note: I am concerned with my lack of discipline grouping
    UPDATE shooter_calculated_grades scg
      JOIN grades g ON scg.grade_id = g.id
      JOIN NumberOfShoots Shoots ON Shoots.shooter_id = scg.shooter_id AND Shoots.discipline_id = g.discipline_id
    SET number_of_shoots = Shoots.shoots;

-- Update audit trail timestamps for grades that have not changed
    UPDATE audit_trails a
      JOIN AuditsOfCalculatedGradesThatHaveNotChanged a2 ON a.id = a2.id
    SET a.`timestamp` = @time;

-- Find the last audit trail for this shooter to determine if there has been any change
    CALL InsertAuditsOfCalculatedGradesThatHaveChanged(@time);

-- Add audit trail changes for updates
    CALL InsertAuditChangesOfCalculatedGradeUpdates(@time);

-- Add audit trail changes for new inserts
    CALL InsertAuditCalculatedGradeChangesForColumn('shooter_id', @time);
    CALL InsertAuditCalculatedGradeChangesForColumn('grade_id', @time);
    CALL InsertAuditCalculatedGradeChangesForColumn('avg_score', @time);
    CALL InsertAuditCalculatedGradeChangesForColumn('number_of_shoots', @time);
  END;
