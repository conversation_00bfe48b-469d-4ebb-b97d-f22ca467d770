ALTER TABLE `events`
DROP FOREIGN KEY `events_ibfk_1`,
ADD FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `event_teams_shooters`
DROP FOREIGN KEY `event_teams_shooters_ibfk_1`,
ADD FOREIGN KEY (`team_id`) REFERENCES `event_teams` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `event_teams_shooters`
DROP FOREIGN KEY `event_teams_shooters_ibfk_2`,
ADD FOREIGN KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `grades`
DROP FOREIGN KEY `grades_ibfk_1`,
ADD FOREIGN KEY (`discipline_id`) REFERENCES `disciplines` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `shooters_grades`
DROP FOREIGN KEY `shooters_grades_ibfk_2`,
ADD FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `shooter_membership`
DROP FOREIGN KEY `shooter_membership_ibfk_2`,
ADD FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `shooter_update_requests`
DROP FOREIGN KEY `shooter_update_requests_ibfk_1`,
ADD FOREIGN KEY (`association_id`) REFERENCES `associations` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `subevents_ranges`
DROP FOREIGN KEY `subevents_ranges_ibfk_6`,
ADD FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `subevents_ranges`
DROP FOREIGN KEY `subevents_ranges_ibfk_7`,
ADD FOREIGN KEY (`range_id`) REFERENCES `ranges` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `team_results`
DROP FOREIGN KEY `team_results_ibfk_1`,
ADD FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `team_results`
DROP FOREIGN KEY `team_results_ibfk_2`,
ADD FOREIGN KEY (`team_id`) REFERENCES `event_teams` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE `team_results`
DROP FOREIGN KEY `team_results_ibfk_3`,
ADD FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
