ALTER TABLE `events` ADD `is_divisional` tinyint(1) NOT NULL DEFAULT '0';

CREATE TABLE `divisions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `long_name` varchar(255) NOT NULL,
  `short_name` varchar(255) NOT NULL,
  `order` tinyint unsigned NOT NULL DEFAULT '10'
) ENGINE='InnoDB';

CREATE TABLE `event_shooter_divisions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `event_id` int(10) unsigned NOT NULL,
  `subevent_id` int(10) unsigned NULL,
  `shooter_id` int(10) unsigned NOT NULL,
  `division_id` int(10) unsigned NOT NULL,
  FOREIGN KEY (`event_id`) REFERENCES `events` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  <PERSON>OREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (`shooter_id`) REFERENCES `shooters` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE='InnoDB';

ALTER TABLE `match_badges`
CHANGE `grade_id` `grade_id` int(10) unsigned NULL AFTER `id`,
ADD `division_id` int(10) unsigned NULL,
ADD FOREIGN KEY (`grade_id`) REFERENCES `grades` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD FOREIGN KEY (`match_id`) REFERENCES `matches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD FOREIGN KEY (`subevent_id`) REFERENCES `subevents` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
ADD FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `shooter_calculated_grades` ENGINE='InnoDB';
