{"translation-revision-date": "2020-09-09 23:57:59+0000", "generator": "GlotPress/3.0.0-alpha.2", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_AU"}, "by %s": ["by %s"], " … ": [" … "], "Display author name": ["Display author name"], "Opacity": ["Opacity"], "Change content position": ["Change content position"], "Minimum height of cover": ["Minimum height of cover"], "25 / 50 / 25": ["25 / 50 / 25"], "33 / 33 / 33": ["33 / 33 / 33"], "Portrait": ["Portrait"], "3:2": ["3:2"], "4:3": ["4:3"], "16:9": ["16:9"], "16:10": ["16:10"], "Landscape": ["Landscape"], "Aspect Ratio": ["Aspect Ratio"], "survey": ["survey"], "social": ["social"], "50 / 50": ["50 / 50"], "This column count exceeds the recommended amount and may cause visual breakage.": ["This column count exceeds the recommended amount and may cause visual breakage."], "Color Settings": ["Colour Settings"], "Browser default": ["Browser default"], "Edit gallery image": ["Edit gallery image"], "Upload external image": ["Upload external image"], "Crop": ["Crop"], "Image uploaded.": ["Image uploaded."], "Rotate": ["Rotate"], "Zoom": ["Zoom"], "Could not edit image. %s": ["Could not edit image. %s"], "2:3": ["2:3"], "3:4": ["3:4"], "9:16": ["9:16"], "10:16": ["10:16"], "70 / 30": ["70 / 30"], "30 / 70": ["30 / 70"], "Display an icon linking to a social media profile or website.": ["Display an icon linking to a social media profile or website."], "%s label": ["%s label"], "Briefly describe the link to help screen reader users.": ["Briefly describe the link to help screen reader users."], "Link label": ["Link label"], "Social Icon": ["Social Icon"], "block keywords\u0004links": ["links"], "Display icons linking to your social media profiles or websites.": ["Display icons linking to your social media profiles or websites."], "Social Icons": ["Social Icons"], "Select poster image": ["Select poster image"], "Poster image": ["Poster image"], "poem": ["poem"], "WHAT was he doing, the great god Pan,\n\tDown in the reeds by the river?\nSpreading ruin and scattering ban,\nSplashing and paddling with hoofs of a goat,\nAnd breaking the golden lilies afloat\n    With the dragon-fly on the river.": ["WHAT was he doing, the great god <PERSON>,\n\tDown in the reeds by the river?\nSpreading ruin and scattering ban,\nSplashing and paddling with hoofs of a goat,\nAnd breaking the golden lilies afloat\n    With the dragon-fly on the river."], "Footer label": ["Footer label"], "Header label": ["Header label"], "Matt Mullenweg": ["<PERSON>"], "EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window which is almost a postage stamp in the frame, other forms appear;": ["EXT. XANADU - FAINT DAWN - 1940 (MINIATURE)\nWindow, very small in the distance, illuminated.\nAll around this is an almost totally black screen. Now, as the camera moves slowly towards the window which is almost a postage stamp in the frame, other forms appear;"], "Unsupported": ["Unsupported"], "Image alignment": ["Image alignment"], "Display featured image": ["Display featured image"], "Featured image settings": ["Featured image settings"], "Full post": ["Full post"], "Show:": ["Show:"], "download": ["download"], "Media file": ["Media file"], "Suspendisse commodo neque lacus, a dictum orci interdum et.": ["Suspendisse commodo neque lacus, a dictum orci interdum et."], "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis.": ["Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent et eros eu felis."], "// A \"block\" is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );": ["// A \"block\" is the abstract term used\n// to describe units of markup that\n// when composed together, form the\n// content or layout of a page.\nregisterBlockType( name, settings );"], "Prompt visitors to take action with a group of button-style links.": ["Prompt visitors to take action with a group of button-style links."], "Buttons": ["Buttons"], "recording": ["recording"], "podcast": ["podcast"], "Link to": ["Link to"], "Gallery settings": ["Gallery settings"], "ADD MEDIA": ["ADD MEDIA"], "Level %1$s. %2$s": ["Level %1$s. %2$s"], "Level %s. Empty.": ["Level %s. Empty."], "(Note: many devices and browsers do not display this text.)": ["(Note: many devices and browsers do not display this text.)"], "Describe the role of this image on the page.": ["Describe the role of this image on the page."], "Title attribute": ["Title attribute"], "Embed a TikTok video.": ["Embed a TikTok video."], "Change heading level": ["Change heading level"], "Image size": ["Image size"], "Pill Shape": ["<PERSON><PERSON>"], "Logos Only": ["Logos Only"], "Grid view": ["Grid view"], "List view": ["List view"], "Post meta settings": ["Post meta settings"], "Sorting and filtering": ["Sorting and filtering"], "Post content": ["Post content"], "Enter address": ["Enter address"], "Post content settings": ["Post content settings"], "Play inline": ["Play inline"], "The current poster image url is %s": ["The current poster image URL is %s"], "There is no poster image currently selected": ["There is no poster image currently selected"], "Note: Autoplaying audio may cause usability issues for some visitors.": ["Note: autoplaying audio may cause usability issues for some visitors."], "Percentage width": ["Percentage width"], "Note: Autoplaying videos may cause usability issues for some visitors.": ["Note: autoplaying videos may cause usability issues for some visitors."], "Column settings": ["Column settings"], "Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim.": ["Nam risus massa, ullamcorper consectetur eros fermentum, porta aliquet ligula. Sed vel mauris nec enim."], "Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit.": ["Etiam et egestas lorem. Vivamus sagittis sit amet dolor quis lobortis. Integer sed fermentum arcu, id vulputate lacus. Etiam fermentum sem eu quam hendrerit."], "Two columns; one-third, two-thirds split": ["Two columns; one-third, two-thirds split"], "Two columns; two-thirds, one-third split": ["Two columns; two-thirds, one-third split"], "Three columns; equal split": ["Three columns; equal split"], "Three columns; wide center column": ["Three columns; wide centre column"], "Two columns; equal split": ["Two columns; equal split"], "Fill": ["Fill"], "Call to Action": ["Call to Action"], "Link rel": ["<PERSON> rel"], "Welcome to the wonderful world of blocks…": ["Welcome to the wonderful world of blocks…"], "Border radius": ["Border radius"], "In quoting others, we cite ourselves.": ["In quoting others, we cite ourselves."], "cite": ["cite"], "One of the hardest things to do in technology is disrupt yourself.": ["One of the hardest things to do in technology is disrupt yourself."], "Ordered list settings": ["Ordered list settings"], "Reverse list numbering": ["Reverse list numbering"], "Start value": ["Start value"], "Upload a file or pick one from your media library.": ["Upload a file or pick one from your media library."], "Open in new tab": ["Open in new tab"], "Header section": ["Header section"], "Footer section": ["Footer section"], "Insert a table for sharing data.": ["Insert a table for sharing data."], "Create Table": ["Create Table"], "Upload an image or video file, or pick one from your media library.": ["Upload an image or video file, or pick one from your media library."], "Align Column Left": ["Align Column Left"], "Align Column Center": ["Align Column Centre"], "Align Column Right": ["Align Column Right"], "Change column alignment": ["Change column alignment"], "Clear Media": ["Clear Media"], "Attachment page": ["Attachment page"], "December 6, 2018": ["December 6, 2018"], "February 21, 2019": ["February 21, 2019"], "May 7, 2019": ["May 7, 2019"], "Release Date": ["Release Date"], "Jazz Musician": ["Jazz Musician"], "<strong>Snow Patrol</strong>": ["<strong>Snow Patrol</strong>"], "Mont Blanc appears—still, snowy, and serene.": ["Mont Blanc appears—still, snowy, and serene."], "— Kobayashi Issa (一茶)": ["<PERSON> <PERSON><PERSON> (一茶)"], "Describe the purpose of the image": ["Describe the purpose of the image"], "Leave empty if the image is purely decorative.": ["Leave empty if the image is purely decorative."], "Crop image to fill entire column": ["Crop image to fill entire column"], "The wren<br>Earns his living<br>Noiselessly.": ["The wren<br>Earns his living<br>Noiselessly."], "Write gallery caption…": ["Write gallery caption…"], "Code is Poetry": ["Code is Poetry"], "Move image forward": ["Move image forward"], "Move image backward": ["Move image backward"], "In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing.": ["In a village of La Mancha, the name of which I have no desire to call to mind, there lived not long since one of those gentlemen that keep a lance in the lance-rack, an old buckler, a lean hack, and a greyhound for coursing."], "Group": ["Group"], "A block that groups other blocks.": ["A block that groups other blocks."], "container": ["container"], "wrapper": ["wrapper"], "row": ["row"], "section": ["section"], "Paste a link to the content you want to display on your site.": ["Paste a link to the content you want to display on your site."], "https://wordpress.org/support/article/embeds/": ["https://wordpress.org/support/article/embeds/"], "Learn more about embeds": ["Learn more about embeds"], "Six.": ["Six."], "Five.": ["Five."], "Four.": ["Four."], "Three.": ["Three."], "Two.": ["Two."], "One.": ["One."], "- Select -": ["- Select -"], "feed": ["feed"], "atom": ["atom"], "Display entries from any RSS or Atom feed.": ["Display entries from any RSS or Atom feed."], "Max number of words in excerpt": ["Max number of words in excerpt"], "Display excerpt": ["Display excerpt"], "Display date": ["Display date"], "Display author": ["Display author"], "Edit RSS URL": ["Edit RSS URL"], "Use URL": ["Use URL"], "Enter URL here…": ["Enter URL here…"], "find": ["find"], "Help visitors find your content.": ["Help visitors find your content."], "Add button text…": ["Add button text…"], "Button text": ["Button text"], "Optional placeholder…": ["Optional placeholder…"], "Optional placeholder text": ["Optional placeholder text"], "Add label…": ["Add label…"], "RSS settings": ["RSS settings"], "Tag Cloud settings": ["Tag Cloud settings"], "Label text": ["Label text"], "Content before this block will be shown in the excerpt on your archives page.": ["Content before this block will be shown in the excerpt on your archives page."], "Hide the excerpt on the full content page": ["Hide the excerpt on the full content page"], "The excerpt is visible.": ["The excerpt is visible."], "The excerpt is hidden.": ["The excerpt is hidden."], "archive": ["archive"], "posts": ["posts"], "A calendar of your site’s posts.": ["A calendar of your site’s posts."], "link": ["link"], "Prompt visitors to take action with a button-style link.": ["Prompt visitors to take action with a button-style link."], "image %1$d of %2$d in gallery": ["image %1$d of %2$d in gallery"], "Embedded content from %s can't be previewed in the editor.": ["Embedded content from %s can't be previewed in the editor."], "Sorry, this content could not be embedded.": ["Sorry, this content could not be embedded."], "Embed Amazon Kindle content.": ["Embed Amazon Kindle content."], "ebook": ["ebook"], "Focal point picker": ["Focal point picker"], "block style\u0004Default": ["<PERSON><PERSON><PERSON>"], "keyboard key\u0004Space": ["Space"], "keyboard key\u0004Backspace": ["Backspace"], "Embed Crowdsignal (formerly Polldaddy) content.": ["Embed Crowdsignal (formerly Polldaddy) content."], "content placeholder\u0004Content…": ["Content…"], "button label\u0004Convert to link": ["Convert to link"], "button label\u0004Try again": ["Try again"], "This image has an empty alt attribute": ["This image has an empty alt attribute"], "This image has an empty alt attribute; its file name is %s": ["This image has an empty alt attribute; its file name is %s"], "Empty block; start writing or type forward slash to choose a block": ["Empty block; start writing or type forward slash to choose a block"], "Paragraph block": ["Paragraph block"], "Start writing or type / to choose a block": ["Start writing or type / to choose a block"], "Page Break": ["Page Break"], "Stack on mobile": ["Stack on mobile"], "Write…": ["Write…"], "poetry": ["poetry"], "Verse": ["Verse"], "New Column": ["New Column"], "Muted": ["Muted"], "movie": ["movie"], "This block is deprecated. Please use the Columns block instead.": ["This block is deprecated. Please use the Columns block instead."], "Text Columns (deprecated)": ["Text Columns (deprecated)"], "Stripes": ["Stripes"], "Insert a table — perfect for sharing charts and data.": ["Insert a table — perfect for sharing charts and data."], "Embed a video from your media library or upload a new one.": ["Embed a video from your media library or upload a new one."], "Insert poetry. Use special spacing formats. Or quote song lyrics.": ["Insert poetry. Use special spacing formats. Or quote song lyrics."], "Video settings": ["Video settings"], "Playback controls": ["Playback controls"], "Delete Column": ["Delete Column"], "Add Column After": ["Add Column After"], "Add Column Before": ["Add Column Before"], "Delete Row": ["Delete Row"], "Add Row After": ["Add Row After"], "Add Row Before": ["Add Row Before"], "Write shortcode here…": ["Write shortcode here…"], "Shortcode": ["Shortcode"], "divider": ["divider"], "horizontal-line": ["horizontal-line"], "Separator": ["Separator"], "Height in pixels": ["Height in pixels"], "Spacer": ["Spacer"], "Write subheading…": ["Write subheading…"], "Fixed width table cells": ["Fixed width table cells"], "Dots": ["Dots"], "Wide Line": ["Wide Line"], "This block is deprecated. Please use the Paragraph block instead.": ["This block is deprecated. Please use the Paragraph block instead."], "Subheading (deprecated)": ["Subheading (deprecated)"], "Row Count": ["Row Count"], "Column Count": ["Column Count"], "blockquote": ["blockquote"], "block style\u0004Large": ["Large"], "Add white space between blocks and customize its height.": ["Add white space between blocks and customise its height."], "Insert additional custom elements with a WordPress shortcode.": ["Insert additional custom elements with a WordPress shortcode."], "Create a break between ideas or sections with a horizontal separator.": ["Create a break between ideas or sections with a horizontal separator."], "Give quoted text visual emphasis. \"In quoting others, we cite ourselves.\" — Julio Cortázar": ["Give quoted text visual emphasis. \"In quoting others, we cite ourselves.\" — <PERSON>"], "Edit table": ["Edit table"], "Spacer settings": ["Spacer settings"], "Table settings": ["Table settings"], "Write citation…": ["Write citation…"], "Write quote…": ["Write quote…"], "Pullquote": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Write preformatted text…": ["Write preformatted text…"], "Give special visual emphasis to a quote from your text.": ["Give special visual emphasis to a quote from your text."], "Solid color": ["Solid colour"], "Main color": ["Main colour"], "text": ["text"], "Write list…": ["Write list…"], "numbered list": ["numbered list"], "Indent list item": ["Indent list item"], "Outdent list item": ["Outdent list item"], "Convert to ordered list": ["Convert to ordered list"], "Convert to unordered list": ["Convert to unordered list"], "recent posts": ["recent posts"], "Latest Posts": ["Latest Posts"], "Display post date": ["Display post date"], "photo": ["photo"], "pagination": ["pagination"], "next page": ["next page"], "Toggle to show a large initial letter.": ["Toggle to show a large initial letter."], "Showing large initial letter.": ["Showing large initial letter."], "Add text that respects your spacing and tabs, and also allows styling.": ["Add text that respects your spacing and tabs, and also allows styling."], "Display a list of your most recent posts.": ["Display a list of your most recent posts."], "recent comments": ["recent comments"], "Latest Comments": ["Latest Comments"], "Keep as HTML": ["Keep as HTML"], "block name\u0004More": ["More"], "Media area": ["Media area"], "Media & Text": ["Media & Text"], "Show media on right": ["Show media on right"], "Show media on left": ["Show media on left"], "Start with the building block of all narrative.": ["Start with the building block of all narrative."], "Separate your content into a multi-page experience.": ["Separate your content into a multi-page experience."], "Create a bulleted or numbered list.": ["Create a bulleted or numbered list."], "Display a list of your most recent comments.": ["Display a list of your most recent comments."], "Insert an image to make a visual statement.": ["Insert an image to make a visual statement."], "Your site doesn’t include support for this block.": ["Your site doesn’t include support for this block."], "Your site doesn’t include support for the \"%s\" block. You can leave this block intact or remove it entirely.": ["Your site doesn’t include support for the \"%s\" block. You can leave this block intact or remove it entirely."], "Your site doesn’t include support for the \"%s\" block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely.": ["Your site doesn’t include support for the \"%s\" block. You can leave this block intact, convert its content to a Custom HTML block, or remove it entirely."], "Set media and words side-by-side for a richer layout.": ["Set media and words side-by-side for a richer layout."], "Text settings": ["Text settings"], "Latest comments settings": ["Latest comments settings"], "Media & Text settings": ["Media & Text settings"], "Drop cap": ["Drop cap"], "Number of comments": ["Number of comments"], "Display avatar": ["Display avatar"], "embed": ["embed"], "subtitle": ["subtitle"], "title": ["title"], "Heading": ["Heading"], "Write heading…": ["Write heading…"], "photos": ["photos"], "images": ["images"], "Edit image": ["Edit image"], "Thumbnails are not cropped.": ["Thumbnails are not cropped."], "Thumbnails are cropped to align.": ["Thumbnails are cropped to align."], "Heading %d": ["Heading %d"], "pdf": ["pdf"], "document": ["document"], "Copy URL": ["Copy URL"], "Write file name…": ["Write file name…"], "Write HTML…": ["Write HTML…"], "button label\u0004Download": ["Download"], "Add custom HTML code and preview it as you edit.": ["Add custom HTML code and preview it as you edit."], "Introduce new sections and organize content to help visitors (and search engines) understand the structure of your content.": ["Introduce new sections and organise content to help visitors (and search engines) understand the structure of your content."], "Display multiple images in a rich gallery.": ["Display multiple images in a rich gallery."], "Add a link to a downloadable file.": ["Add a link to a downloadable file."], "Drag images, upload new ones or select files from your library.": ["Drag images, upload new ones or select files from your library."], "Text link settings": ["Text link settings"], "Show download button": ["Show download button"], "Download button settings": ["Download button settings"], "Image settings": ["Image settings"], "Alt text (alternative text)": ["Alt text (alternative text)"], "Crop images": ["Crop images"], "video": ["video"], "audio": ["audio"], "music": ["music"], "image": ["image"], "blog": ["blog"], "post": ["post"], "Embedded content from %s": ["Embedded content from %s"], "Enter URL to embed here…": ["Enter URL to embed here…"], "%s URL": ["%s URL"], "Embedding…": ["Embedding…"], "Write title…": ["Write title…"], "Add a block that displays content pulled from other sites, like Twitter, Instagram or YouTube.": ["Add a block that displays content pulled from other sites, like Twitter, Instagram or YouTube."], "Edit URL": ["Edit URL"], "Overlay": ["Overlay"], "button label\u0004Embed": ["Embed"], "block title\u0004Embed": ["Embed"], "Embed videos, images, tweets, audio, and other content from external sources.": ["Embed videos, images, tweets, audio, and other content from external sources."], "Resize for smaller devices": ["Resize for smaller devices"], "This embed may not preserve its aspect ratio when the browser is resized.": ["This embed may not preserve its aspect ratio when the browser is resized."], "This embed will preserve its aspect ratio when the browser is resized.": ["This embed will preserve its aspect ratio when the browser is resized."], "Add an image or video with a text overlay — great for headers.": ["Add an image or video with a text overlay — great for headers."], "Embed an Animoto video.": ["Embed an Animoto video."], "Embed a Vimeo video.": ["Embed a Vimeo video."], "Embed Flickr content.": ["Embed Flickr content."], "Embed Spotify content.": ["Embed Spotify content."], "Embed SoundCloud content.": ["Embed SoundCloud content."], "Embed an Instagram post.": ["Embed an Instagram post."], "Embed a Facebook post.": ["Embed a Facebook post."], "Embed a WordPress.tv video.": ["Embed a WordPress.tv video."], "Embed a VideoPress video.": ["Embed a VideoPress video."], "Embed a Tumblr post.": ["Embed a Tumblr post."], "Embed a TED video.": ["Embed a TED video."], "Embed Speaker Deck content.": ["Embed Speaker Deck content."], "Embed a YouTube video.": ["Embed a YouTube video."], "Embed SmugMug content.": ["Embed SmugMug content."], "Embed Slideshare content.": ["Embed Slideshare content."], "Embed Scribd content.": ["Embed Scribd content."], "Embed Screencast content.": ["Embed Screencast content."], "Embed ReverbNation content.": ["Embed ReverbNation content."], "Embed a Reddit thread.": ["Embed a Reddit thread."], "Embed Polldaddy content.": ["Embed Polldaddy content."], "Embed Mixcloud content.": ["Embed Mixcloud content."], "Embed a tweet.": ["Embed a tweet."], "Embed Meetup.com content.": ["Embed Meetup.com content."], "Embed Kickstarter content.": ["Embed Kickstarter content."], "Embed Issuu content.": ["Embed Issuu content."], "Embed Imgur content.": ["Embed Imgur content."], "Embed a Dailymotion video.": ["Embed a Dailymotion video."], "Embed CollegeHumor content.": ["Embed CollegeHumor content."], "Embed Cloudup content.": ["Embed Cloudup content."], "Media settings": ["Media settings"], "Border settings": ["Border settings"], "Fixed background": ["Fixed background"], "Add a block that displays content in multiple columns, then add whatever content blocks you’d like.": ["Add a block that displays content in multiple columns, then add whatever content blocks you’d like."], "Cover": ["Cover"], "Classic": ["Classic"], "Write code…": ["Write code…"], "Add text…": ["Add text…"], "Button": ["<PERSON><PERSON>"], "A single column within a columns block.": ["A single column within a columns block."], "Outline": ["Outline"], "button to expand options\u0004More": ["More"], "block title\u0004Classic": ["Classic"], "block style\u0004Rounded": ["Rounded"], "Display code snippets that respect your spacing and tabs.": ["Display code snippets that respect your spacing and tabs."], "Use the classic WordPress editor.": ["Use the classic WordPress editor."], "Display a list of all categories.": ["Display a list of all categories."], "Categories settings": ["Categories settings"], "Color settings": ["Colour settings"], "Create and save content to reuse across your site. Update the block, and the changes apply everywhere it’s used.": ["Create and save content to reuse across your site. Update the block, and the changes apply everywhere it’s used."], "Block has been deleted or is unavailable.": ["Block has been deleted or is unavailable."], "Write caption…": ["Write caption…"], "Reusable Block": ["Reusable Block"], "Display a monthly archive of your posts.": ["Display a monthly archive of your posts."], "Embed a simple audio player.": ["Embed a simple audio player."], "Archives settings": ["Archives settings"], "Audio settings": ["Audio settings"], "Link settings": ["Link settings"], "Copied!": ["Copied!"], "Replace image": ["Replace image"], "Number of items": ["Number of items"], "Gallery": ["Gallery"], "Custom HTML": ["Custom HTML"], "Original": ["Original"], "(Untitled)": ["(Untitled)"], "Read more": ["Read more"], "Embed a WordPress post.": ["Embed a WordPress post."], "Taxonomy": ["Taxonomy"], "Color": ["Colour"], "editor button\u0004Left to right": ["Left to right"], "Auto": ["Auto"], "Metadata": ["<PERSON><PERSON><PERSON>"], "Autoplay": ["Autoplay"], "Background color": ["Background colour"], "Replace": ["Replace"], "Text color": ["Text colour"], "Page break": ["Page break"], "Table": ["Table"], "bullet list": ["bullet list"], "File": ["File"], "A cloud of your most used tags.": ["A cloud of your most used tags."], "Empty": ["Empty"], "Video": ["Video"], "Audio": ["Audio"], "Columns": ["Columns"], "Media File": ["Media File"], "Attachment Page": ["Attachment Page"], "Insert Media": ["Insert Media"], "Background Color": ["Background Colour"], "Text Color": ["Text Colour"], "Remove image": ["Remove Image"], "Remove": ["Remove"], "Link": ["Link"], "Square": ["Square"], "Preload": ["Preload"], "Display as dropdown": ["Display as dropdown"], "Show post counts": ["Show post counts"], "Calendar": ["Calendar"], "Show hierarchy": ["Show hierarchy"], "Tag Cloud": ["Tag Cloud"], "Column": ["Column"], "Paragraph": ["Paragraph"], "Preformatted": ["Preformatted"], "Code": ["Code"], "Dimensions": ["Dimensions"], "List": ["List"], "Loop": ["Loop"], "Quote": ["Quote"], "Image": ["Image"], "ordered list": ["ordered list"], "Unlink": ["Unlink"], "Name:": ["Name:"], "sound": ["sound"], "Default": ["<PERSON><PERSON><PERSON>"], "HTML": ["HTML"], "Search": ["Search"], "Categories": ["Categories"], "Save": ["Save"], "URL": ["URL"], "Cancel": ["Cancel"], "Edit": ["Edit"], "Archives": ["Archives"], "RSS": ["RSS"], "None": ["None"], "Apply": ["Apply"], "Preview": ["Preview"], "Version": ["Version"], "Select": ["Select"], "Excerpt": ["Excerpt"], "(no title)": ["(no title)"], "No posts found.": ["No posts found."]}}, "comment": {"reference": "wp-includes/js/dist/block-library.js"}}