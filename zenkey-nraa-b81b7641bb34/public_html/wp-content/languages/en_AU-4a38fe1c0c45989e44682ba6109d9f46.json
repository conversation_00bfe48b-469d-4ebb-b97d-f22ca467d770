{"translation-revision-date": "2020-09-09 23:59:49+0000", "generator": "GlotPress/3.0.0-alpha.2", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_AU"}, "All site health tests have finished running. There are items that should be addressed, and the results are now available on the page.": ["All site health tests have finished running. There are items that should be addressed, and the results are now available on the page."], "Should be improved": ["Should be improved"], "Good": ["Good"], "%s critical issue": ["%s critical issue", "%s critical issues"], "%s item with no issues detected": ["%s item with no issues detected", "%s items with no issues detected"], "%s recommended improvement": ["%s recommended improvement", "%s recommended improvements"], "All site health tests have finished running.": ["All site health tests have finished running."], "Please wait...": ["Please wait…"], "All site health tests have finished running. Your site is looking good, and the results are now available on the page.": ["All site health tests have finished running. Your site is looking good, and the results are now available on the page."], "Site information has been copied to your clipboard.": ["Site information has been copied to your clipboard."]}}, "comment": {"reference": "wp-admin/js/site-health.js"}}