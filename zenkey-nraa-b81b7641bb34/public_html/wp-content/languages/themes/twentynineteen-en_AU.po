# Translation of Themes - Twenty Nineteen in English (Australia)
# This file is distributed under the same license as the Themes - Twenty Nineteen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-05-11 03:19:15+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_AU\n"
"Project-Id-Version: Themes - Twenty Nineteen\n"

#. Description of the theme
msgid "Our 2019 default theme is designed to show off the power of the block editor. It features custom styles for all the default blocks, and is built so that what you see in the editor looks like what you'll see on your website. Twenty Nineteen is designed to be adaptable to a wide range of websites, whether you’re running a photo blog, launching a new business, or supporting a non-profit. Featuring ample whitespace and modern sans-serif headlines paired with classic serif body text, it's built to be beautiful on all screen sizes."
msgstr "Our 2019 default theme is designed to show off the power of the block editor. It features custom styles for all the default blocks, and is built so that what you see in the editor looks like what you'll see on your website. Twenty Nineteen is designed to be adaptable to a wide range of websites, whether you’re running a photo blog, launching a new business, or supporting a non-profit. Featuring ample whitespace and modern sans-serif headlines paired with classic serif body text, it's built to be beautiful on all screen sizes."

#. Theme Name of the theme
msgid "Twenty Nineteen"
msgstr "Twenty Nineteen"

#. translators: %s: parent post link
#: single.php:31
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%s</span>"
msgstr "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%s</span>"

#: image.php:87
msgctxt "Parent post link"
msgid "<span class=\"meta-nav\">Published in</span><br><span class=\"post-title\">%title</span>"
msgstr "<span class=\"meta-nav\">Published in</span><br><span class=\"post-title\">%title</span>"

#: template-parts/content/content.php:18
#: template-parts/content/content-excerpt.php:18
msgctxt "post"
msgid "Featured"
msgstr "Featured"

#: inc/back-compat.php:39 inc/back-compat.php:53 inc/back-compat.php:73
msgid "Twenty Nineteen requires at least WordPress version 4.7. You are running version %s. Please upgrade and try again."
msgstr "Twenty Nineteen requires WordPress version 4.7+ or higher. You are running version %s. Upgrade it and try again."

#: inc/template-functions.php:216
msgid "Back"
msgstr "Back"

#: inc/template-functions.php:209
msgid "More"
msgstr "More"

#: inc/customizer.php:98
msgid "Apply a filter to featured images using the primary color"
msgstr "Apply a filter to featured images using the primary colour"

#: inc/customizer.php:78
msgid "Apply a custom color for buttons, links, featured images, etc."
msgstr "Apply a custom colour for buttons, links, featured images, etc."

#: inc/customizer.php:56
msgctxt "primary color"
msgid "Custom"
msgstr "Custom"

#: inc/customizer.php:55
msgctxt "primary color"
msgid "Default"
msgstr "Default"

#: functions.php:166
msgid "White"
msgstr "White"

#: functions.php:161
msgid "Light Gray"
msgstr "Light Grey"

#: functions.php:156
msgid "Dark Gray"
msgstr "Dark Grey"

#: functions.php:151
msgid "Secondary"
msgstr "Secondary"

#: functions.php:134
msgid "XL"
msgstr "XL"

#: functions.php:133
msgid "Huge"
msgstr "Huge"

#: functions.php:128
msgid "L"
msgstr "L"

#: functions.php:127
msgid "Large"
msgstr "Large"

#: functions.php:122
msgid "M"
msgstr "M"

#: functions.php:121
msgid "Normal"
msgstr "Normal"

#: functions.php:115
msgid "Small"
msgstr "Small"

#: functions.php:116
msgid "S"
msgstr "S"

#: functions.php:60 footer.php:37
msgid "Footer Menu"
msgstr "Footer Menu"

#: image.php:70
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "Full size"

#: image.php:56
msgid "Page"
msgstr "Page"

#: functions.php:190
msgid "Add widgets here to appear in your footer."
msgstr "Add widgets here to appear in your footer."

#: functions.php:188 template-parts/footer/footer-widgets.php:12
msgid "Footer"
msgstr "Footer"

#: inc/customizer.php:53
msgid "Primary Color"
msgstr "Primary Colour"

#: template-parts/post/discussion-meta.php:18
msgid "No comments"
msgstr "No comments"

#. translators: %1(X comments)$s
#: template-parts/post/discussion-meta.php:16
msgid "%d Comment"
msgid_plural "%d Comments"
msgstr[0] "%d Comment"
msgstr[1] "%d Comments"

#: template-parts/post/author-bio.php:26
msgid "View more posts"
msgstr "View more posts"

#. translators: %s: post author
#: template-parts/post/author-bio.php:17
msgid "Published by %s"
msgstr "Published by %s"

#: template-parts/header/site-branding.php:33
msgid "Top Menu"
msgstr "Top Menu"

#. translators: %s: Name of current post. Only visible to screen readers
#: template-parts/content/content.php:36
#: template-parts/content/content-single.php:27
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"

#: image.php:52 template-parts/content/content-page.php:27
#: template-parts/content/content.php:49
#: template-parts/content/content-single.php:40
msgid "Pages:"
msgstr "Pages:"

#: template-parts/content/content-none.php:46
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."

#: template-parts/content/content-none.php:39
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Sorry, but nothing matched your search terms. Please try again with some different keywords."

#. translators: 1: link to WP admin new post page.
#: template-parts/content/content-none.php:26
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."

#: template-parts/content/content-none.php:16
msgid "Nothing Found"
msgstr "Nothing Found"

#: single.php:42
msgid "Previous post:"
msgstr "Previous post:"

#: single.php:41
msgid "Previous Post"
msgstr "Previous Post"

#: single.php:39
msgid "Next post:"
msgstr "Next post:"

#: single.php:38
msgid "Next Post"
msgstr "Next Post"

#: search.php:22
msgid "Search results for:"
msgstr "Search results for:"

#: inc/template-tags.php:234
msgid "Older posts"
msgstr "Older posts"

#: inc/template-tags.php:230
msgid "Newer posts"
msgstr "Newer posts"

#: inc/template-tags.php:104
msgid "Tags:"
msgstr "Tags:"

#: inc/template-tags.php:92
msgid "Posted in"
msgstr "Posted in"

#. translators: used between list items, there is a space after the comma.
#: inc/template-tags.php:86 inc/template-tags.php:98
msgid ", "
msgstr ", "

#. translators: %s: Name of current post. Only visible to screen readers.
#: inc/template-tags.php:63
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"

#: inc/template-tags.php:46
msgid "Posted by"
msgstr "Posted by"

#: inc/template-functions.php:82
msgctxt "monthly archives date format"
msgid "F Y"
msgstr "F Y"

#: inc/template-functions.php:80
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#: inc/template-functions.php:92
msgid "Archives:"
msgstr "Archives:"

#. translators: %s: Taxonomy singular name
#: inc/template-functions.php:90
msgid "%s Archives:"
msgstr "%s Archives:"

#: inc/template-functions.php:86
msgid "Post Type Archives: "
msgstr "Post Type Archives: "

#: inc/template-functions.php:84
msgid "Daily Archives: "
msgstr "Daily Archives: "

#: inc/template-functions.php:82
msgid "Monthly Archives: "
msgstr "Monthly Archives: "

#: inc/template-functions.php:80
msgid "Yearly Archives: "
msgstr "Yearly Archives: "

#: inc/template-functions.php:78
msgid "Author Archives: "
msgstr "Author Archives: "

#: inc/template-functions.php:76
msgid "Tag Archives: "
msgstr "Tag Archives: "

#: inc/template-functions.php:74
msgid "Category Archives: "
msgstr "Category Archives: "

#. translators: %s: Name of current post. Only visible to screen readers
#. translators: %s: Name of current post. Only visible to screen readers.
#: template-parts/content/content-page.php:41
#: template-parts/header/entry-header.php:32 inc/template-tags.php:120
msgid "Edit <span class=\"screen-reader-text\">%s</span>"
msgstr "Edit <span class=\"screen-reader-text\">%s</span>"

#: header.php:25
msgid "Skip to content"
msgstr "Skip to content"

#: functions.php:61 template-parts/header/site-branding.php:46
msgid "Social Links Menu"
msgstr "Social Links Menu"

#: functions.php:59 functions.php:146
msgid "Primary"
msgstr "Primary"

#. translators: %s: WordPress.
#: footer.php:28
msgid "Proudly powered by %s."
msgstr "Proudly powered by %s."

#: comments.php:116
msgid "Comments are closed."
msgstr "Comments are closed."

#: comments.php:96
msgid "Next"
msgstr "Next"

#: comments.php:95
msgid "Previous"
msgstr "Previous"

#: comments.php:92 comments.php:95 comments.php:96
msgid "Comments"
msgstr "Comments"

#. translators: 1: number of comments, 2: post title
#: comments.php:44
msgctxt "comments title"
msgid "%1$s reply on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s replies on &ldquo;%2$s&rdquo;"
msgstr[0] "%1$s reply on &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s replies on &ldquo;%2$s&rdquo;"

#. translators: %s: post title
#: comments.php:40
msgctxt "comments title"
msgid "One reply on &ldquo;%s&rdquo;"
msgstr "One reply on &ldquo;%s&rdquo;"

#: comments.php:35 comments.php:105 comments.php:107
msgid "Leave a comment"
msgstr "Leave a comment"

#: comments.php:33
msgid "Join the Conversation"
msgstr "Join the Conversation"

#: classes/class-twentynineteen-walker-comment.php:99
msgid "Your comment is awaiting moderation."
msgstr "Your comment is awaiting moderation."

#: classes/class-twentynineteen-walker-comment.php:94
msgid "Edit"
msgstr "Edit"

#. translators: 1: comment date, 2: comment time
#: classes/class-twentynineteen-walker-comment.php:86
msgid "%1$s at %2$s"
msgstr "%1$s at %2$s"

#: classes/class-twentynineteen-walker-comment.php:66
msgid "%s <span class=\"screen-reader-text says\">says:</span>"
msgstr "%s <span class=\"screen-reader-text says\">says:</span>"

#: 404.php:24
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "It looks like nothing was found at this location. Maybe try a search?"

#: 404.php:20
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! That page can&rsquo;t be found."

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentynineteen/"
msgstr "https://en-au.wordpress.org/themes/twentynineteen/"

#. Author of the theme
msgid "the WordPress team"
msgstr "the WordPress team"

#. Author URI of the theme
#: footer.php:25
msgid "https://wordpress.org/"
msgstr "https://en-au.wordpress.org/"