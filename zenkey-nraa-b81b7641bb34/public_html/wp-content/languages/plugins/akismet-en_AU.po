# Translation of Plugins - Akismet Anti-Spam - Stable (latest release) in English (Australia)
# This file is distributed under the same license as the Plugins - Akismet Anti-Spam - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2020-03-17 21:00:08+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: en_AU\n"
"Project-Id-Version: Plugins - Akismet Anti-Spam - Stable (latest release)\n"

#: class.akismet-admin.php:691
msgid "No comment history."
msgstr "No comment history."

#: class.akismet-admin.php:659
msgid "<PERSON><PERSON><PERSON> was unable to recheck this comment."
msgstr "<PERSON><PERSON><PERSON> was unable to recheck this comment."

#: class.akismet-admin.php:651
msgid "<PERSON><PERSON><PERSON> was unable to check this comment but will automatically retry later."
msgstr "<PERSON><PERSON><PERSON> was unable to check this comment but will automatically retry later."

#: class.akismet-admin.php:622
msgid "Comment was caught by %s."
msgstr "Comment was caught by %s."

#: class.akismet.php:552
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet is not configured. Please enter an API key."

#: views/enter.php:8
msgid "Enter your API key"
msgstr "Enter your API key"

#: views/connect-jp.php:66
msgid "Set up a different account"
msgstr "Set up a different account"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Set up your Akismet account to enable spam filtering on this site."

#: class.akismet-admin.php:1106
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet could not recheck your comments for spam."

#: class.akismet-admin.php:444
msgid "You don't have permission to do that."
msgstr "You don't have permission to do that."

#: class.akismet-cli.php:165
msgid "Stats response could not be decoded."
msgstr "Stats response could not be decoded."

#: class.akismet-cli.php:159
msgid "Currently unable to fetch stats. Please try again."
msgstr "Currently unable to fetch stats. Please try again."

#: class.akismet-cli.php:134
msgid "API key must be set to fetch stats."
msgstr "API key must be set to fetch stats."

#: views/notice.php:144
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms. This feature is disabled by default, however, you can turn it on below."
msgstr "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms. This feature is disabled by default, however, you can turn it on below."

#: views/config.php:162
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms. This feature is disabled by default, however, you can turn it on above."
msgstr "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms. This feature is disabled by default, however, you can turn it on above."

#: views/notice.php:145
msgid " Please <a href=\"%s\">enable</a> or <a href=\"%s\">disable</a> this feature. <a href=\"%s\" id=\"akismet-privacy-notice-control-notice-info-link\" target=\"_blank\">More information</a>."
msgstr " Please <a href=\"%s\">enable</a> or <a href=\"%s\">disable</a> this feature. <a href=\"%s\" id=\"akismet-privacy-notice-control-notice-info-link\" target=\"_blank\">More information</a>."

#: views/notice.php:143
msgid "Akismet & Privacy."
msgstr "Akismet & Privacy."

#: views/config.php:160
msgid "Do not display privacy notice."
msgstr "Do not display privacy notice."

#: views/config.php:159
msgid "Display a privacy notice under your comment forms."
msgstr "Display a privacy notice under your comment forms."

#: views/config.php:158
msgid "Akismet privacy notice"
msgstr "Akismet privacy notice"

#: views/config.php:155
msgid "Privacy"
msgstr "Privacy"

#: class.akismet.php:1491
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed</a>."
msgstr "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed</a>."

#: class.akismet-admin.php:96
msgid "We collect information about visitors who comment on Sites that use our Akismet anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "We collect information about visitors who comment on Sites that use our Akismet anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."

#: class.akismet.php:236
msgid "Comment discarded."
msgstr "Comment discarded."

#: class.akismet-rest-api.php:174
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "This site's API key is hardcoded and cannot be deleted."

#: class.akismet-rest-api.php:158
msgid "The value provided is not a valid and registered API key."
msgstr "The value provided is not a valid and registered API key."

#: class.akismet-rest-api.php:152
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "This site's API key is hardcoded and cannot be changed via the API."

#: class.akismet-rest-api.php:71 class.akismet-rest-api.php:80
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "The time period for which to retrieve stats. Options: 60-days, 6-months, all"

#: class.akismet-rest-api.php:56
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "If true, show the number of approved comments beside each comment author in the comments list page."

#: class.akismet-rest-api.php:51
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."

#: class.akismet-rest-api.php:27 class.akismet-rest-api.php:101
#: class.akismet-rest-api.php:114 class.akismet-rest-api.php:127
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "A 12-character Akismet API key. Available at akismet.com/get/"

#: class.akismet-admin.php:420
msgid "(%1$s%)"
msgstr "(%1$s%)"

#: views/notice.php:55
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Your site can&#8217;t connect to the Akismet servers."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "An Akismet API key has been defined in the %s file for this site."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Manual Configuration"

#: class.akismet-admin.php:239
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "On this page, you are able to update your Akismet settings and view spam stats."

#. Description of the plugin
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."

#. Plugin Name of the plugin
#: class.akismet-admin.php:121 class.akismet-admin.php:124
msgid "Akismet Anti-Spam"
msgstr "Akismet Anti-Spam"

#: views/stats.php:4
msgid "Akismet Settings"
msgstr "Akismet Settings"

#: views/enter.php:9
msgid "Connect with API key"
msgstr "Connect with API key"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:23 views/connect-jp.php:58
msgid "You are connected as %s."
msgstr "You are connected as %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:31
#: views/connect-jp.php:53 views/connect-jp.php:65
msgid "Connect with Jetpack"
msgstr "Connect with Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:25 views/connect-jp.php:48
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Use your Jetpack connection to set up Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Eliminate spam from your site"

#: views/notice.php:107
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Would you like to <a href=\"%s\">check pending comments</a>?"

#: views/notice.php:105
msgid "Akismet is now protecting your site from spam. Happy blogging!"
msgstr "Akismet is now protecting your site from spam. Happy blogging!"

#: views/setup.php:3 views/notice.php:14
msgid "Set up your Akismet account"
msgstr "Set up your Akismet account"

#: views/config.php:26
msgid "Detailed Stats"
msgstr "Detailed Stats"

#: views/config.php:22
msgid "Statistics"
msgstr "Statistics"

#: class.akismet-admin.php:1218
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."

#: class.akismet-admin.php:1215
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."

#: class.akismet-admin.php:1099
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s comment was caught as spam."
msgstr[1] "%s comments were caught as spam."

#: class.akismet-admin.php:1096
msgid "No comments were caught as spam."
msgstr "No comments were caught as spam."

#: class.akismet-admin.php:1092
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet checked %s comment."
msgstr[1] "Akismet checked %s comments."

#: class.akismet-admin.php:1089
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "There were no comments to check. Akismet will only check comments awaiting moderation."

#: class.akismet-admin.php:419
msgid "Checking for Spam"
msgstr "Checking for Spam"

#: class.akismet.php:558
msgid "Comment not found."
msgstr "Comment not found."

#: class.akismet-cli.php:88
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d comment could not be checked."
msgstr[1] "%d comments could not be checked."

#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d comment moved to Spam."
msgstr[1] "%d comments moved to Spam."

#: class.akismet-cli.php:84
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Processed %d comment."
msgstr[1] "Processed %d comments."

#: class.akismet-cli.php:46
msgid "Comment #%d could not be checked."
msgstr "Comment #%d could not be checked."

#: class.akismet-cli.php:43
msgid "Failed to connect to Akismet."
msgstr "Failed to connect to Akismet."

#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Comment #%d is not spam."

#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Comment #%d is spam."

#: views/config.php:49
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s false positive"
msgstr[1] "%s false positives"

#: views/config.php:47
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s missed spam"
msgstr[1] "%s missed spam"

#: views/notice.php:85
msgid "You don&#8217;t have an Akismet plan."
msgstr "You don&#8217;t have an Akismet plan."

#: views/notice.php:70
msgid "Your Akismet subscription is suspended."
msgstr "Your Akismet subscription is suspended."

#: views/notice.php:65
msgid "Your Akismet plan has been cancelled."
msgstr "Your Akismet plan has been cancelled."

#: views/notice.php:61
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."

#: views/notice.php:60
msgid "Please update your payment information."
msgstr "Please update your payment information."

#: views/notice.php:17
msgid "<strong>Almost done</strong> - configure Akismet and say goodbye to spam"
msgstr "<strong>Almost done</strong> - configure Akismet and say goodbye to spam"

#: class.akismet-admin.php:1019
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet has saved you %d minute!"
msgstr[1] "Akismet has saved you %d minutes!"

#: class.akismet-admin.php:1017
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet has saved you %d hour!"
msgstr[1] "Akismet has saved you %d hours!"

#: class.akismet-admin.php:1015
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet has saved you %s day!"
msgstr[1] "Akismet has saved you %s days!"

#: class.akismet-admin.php:187 class.akismet-admin.php:225
#: class.akismet-admin.php:238
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet filters out spam, so you can focus on more important things."

#: views/notice.php:135
msgid "To continue your service, <a href=\"%s\" target=\"_blank\">upgrade to an Enterprise subscription</a>, which covers an unlimited number of sites."
msgstr "To continue your service, <a href=\"%s\" target=\"_blank\">upgrade to an Enterprise subscription</a>, which covers an unlimited number of sites."

#: views/notice.php:128
msgid "Your Pro subscription allows the use of Akismet on only one site. Please <a href=\"%s\" target=\"_blank\">purchase additional Pro subscriptions</a> or upgrade to an Enterprise subscription that allows the use of Akismet on unlimited sites."
msgstr "Your Pro subscription allows the use of Akismet on only one site. Please <a href=\"%s\" target=\"_blank\">purchase additional Pro subscriptions</a> or upgrade to an Enterprise subscription that allows the use of Akismet on unlimited sites."

#: views/notice.php:121
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."

#: views/notice.php:120
msgid "The API key you entered could not be verified."
msgstr "The API key you entered could not be verified."

#: views/notice.php:116
msgid "Your API key is no longer valid. Please enter a new key <NAME_EMAIL>."
msgstr "Your API key is no longer valid. Please enter a new key <NAME_EMAIL>."

#: views/notice.php:89 views/notice.php:130 views/notice.php:137
msgid "Please <a href=\"%s\" target=\"_blank\">contact our support team</a> with any questions."
msgstr "Please <a href=\"%s\" target=\"_blank\">contact our support team</a> with any questions."

#: views/notice.php:87
msgid "In 2012, Akismet began using subscription plans for all accounts (even free ones). A plan has not been assigned to your account, and we&#8217;d appreciate it if you&#8217;d <a href=\"%s\" target=\"_blank\">sign into your account</a> and choose one."
msgstr "In 2012, Akismet began using subscription plans for all accounts (even free ones). A plan has not been assigned to your account, and we&#8217;d appreciate it if you&#8217;d <a href=\"%s\" target=\"_blank\">sign into your account</a> and choose one."

#: views/config.php:95
msgid "All systems functional."
msgstr "All systems functional."

#: views/config.php:95
msgid "Enabled."
msgstr "Enabled."

#: views/config.php:92
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."

#: views/config.php:92
msgid "Temporarily disabled."
msgstr "Temporarily disabled."

#: views/config.php:86
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."

#: views/config.php:86
msgid "Disabled."
msgstr "Disabled."

#: views/config.php:79
msgid "SSL Status"
msgstr "SSL Status"

#: class.akismet-admin.php:637
msgid "This comment was reported as not spam."
msgstr "This comment was reported as not spam."

#: class.akismet-admin.php:629
msgid "This comment was reported as spam."
msgstr "This comment was reported as spam."

#: class.akismet-admin.php:156
msgid "(undo)"
msgstr "(undo)"

#: class.akismet-admin.php:155
msgid "URL removed"
msgstr "URL removed"

#: class.akismet-admin.php:154
msgid "Removing..."
msgstr "Removing..."

#: class.akismet-admin.php:95 class.akismet-admin.php:1237
msgid "Akismet"
msgstr "Akismet"

#: views/config.php:60 class.akismet-admin.php:114 class.akismet-admin.php:246
#: class.akismet-admin.php:698
msgid "Settings"
msgstr "Settings"

#: class.akismet-admin.php:157
msgid "Re-adding..."
msgstr "Re-adding..."

#: class.akismet-admin.php:184 class.akismet-admin.php:222
#: class.akismet-admin.php:235
msgid "Overview"
msgstr "Overview"

#: class.akismet-admin.php:186 class.akismet-admin.php:197
#: class.akismet-admin.php:208
msgid "Akismet Setup"
msgstr "Akismet Setup"

#: class.akismet-admin.php:188
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "On this page, you are able to set up the Akismet plugin."

#: class.akismet-admin.php:195
msgid "New to Akismet"
msgstr "New to Akismet"

#: class.akismet-admin.php:198
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "You need to enter an API key to activate the Akismet service on your site."

#: class.akismet-admin.php:206
msgid "Enter an API Key"
msgstr "Enter an API Key"

#: class.akismet-admin.php:199
msgid "Sign up for an account on %s to get an API Key."
msgstr "Sign up for an account on %s to get an API Key."

#: class.akismet-admin.php:209
msgid "If you already have an API key"
msgstr "If you already have an API key"

#: class.akismet-admin.php:211
msgid "Copy and paste the API key into the text field."
msgstr "Copy and paste the API key into the text field."

#: class.akismet-admin.php:212
msgid "Click the Use this Key button."
msgstr "Click the Use this Key button."

#: class.akismet-admin.php:224
msgid "Akismet Stats"
msgstr "Akismet Stats"

#: class.akismet-admin.php:226
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "On this page, you are able to view stats on spam filtered on your site."

#: class.akismet-admin.php:237 class.akismet-admin.php:248
#: class.akismet-admin.php:261
msgid "Akismet Configuration"
msgstr "Akismet Configuration"

#: views/config.php:70 class.akismet-admin.php:249
msgid "API Key"
msgstr "API Key"

#: class.akismet-admin.php:249
msgid "Enter/remove an API key."
msgstr "Enter/remove an API key."

#: views/config.php:105 class.akismet-admin.php:250
msgid "Comments"
msgstr "Comments"

#: class.akismet-admin.php:250
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Show the number of approved comments beside each comment author in the comments list page."

#: class.akismet-admin.php:251
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Choose to either discard the worst spam automatically or to always put all spam in spam folder."

#: views/config.php:128 class.akismet-admin.php:251
msgid "Strictness"
msgstr "Strictness"

#: views/config.php:188 class.akismet-admin.php:259
msgid "Account"
msgstr "Account"

#: views/config.php:196 class.akismet-admin.php:262
msgid "Subscription Type"
msgstr "Subscription Type"

#: class.akismet-admin.php:263
msgid "The subscription status - active, cancelled or suspended"
msgstr "The subscription status - active, cancelled or suspended"

#: views/config.php:203 class.akismet-admin.php:263
msgid "Status"
msgstr "Status"

#: class.akismet-admin.php:262
msgid "The Akismet subscription plan"
msgstr "The Akismet subscription plan"

#: class.akismet-admin.php:273
msgid "Akismet FAQ"
msgstr "Akismet FAQ"

#: class.akismet-admin.php:272
msgid "For more information:"
msgstr "For more information:"

#: class.akismet-admin.php:274
msgid "Akismet Support"
msgstr "Akismet Support"

#: class.akismet-admin.php:280
msgid "Cheatin&#8217; uh?"
msgstr "Cheatin&#8217; uh?"

#: class.akismet-admin.php:350
msgctxt "comments"
msgid "Spam"
msgstr "Spam"

#: class.akismet-admin.php:352
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgstr[1] "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."

#: class.akismet-admin.php:362
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgstr[1] "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "

#: class.akismet-admin.php:368
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "

#: class.akismet-admin.php:374
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgstr[1] "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."

#: class.akismet-admin.php:380
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."

#: class.akismet-admin.php:616
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet re-checked and cleared this comment."

#: class.akismet-admin.php:610
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet re-checked and caught this comment as spam."

#: class.akismet-admin.php:426
msgid "Check for Spam"
msgstr "Check for Spam"

#: class.akismet-admin.php:656
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet was unable to recheck this comment (response: %s)."

#: class.akismet-admin.php:540
msgid "Flagged as spam by Akismet"
msgstr "Flagged as spam by Akismet"

#: class.akismet-admin.php:536
msgid "Awaiting spam check"
msgstr "Awaiting spam check"

#: class.akismet-admin.php:546
msgid "Flagged as spam by %s"
msgstr "Flagged as spam by %s"

#: class.akismet-admin.php:542
msgid "Cleared by Akismet"
msgstr "Cleared by Akismet"

#: class.akismet-admin.php:548
msgid "Un-spammed by %s"
msgstr "Un-spammed by %s"

#: class.akismet-admin.php:583
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s approved"
msgstr[1] "%s approved"

#: class.akismet-admin.php:560
msgid "History"
msgstr "History"

#: class.akismet-admin.php:560 class.akismet-admin.php:568
msgid "View comment history"
msgstr "View comment history"

#: class.akismet-admin.php:905
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."

#: class.akismet-admin.php:682
msgid "%s ago"
msgstr "%s ago"

#: class.akismet-admin.php:1012
msgid "Cleaning up spam takes time."
msgstr "Cleaning up spam takes time."

#: class.akismet-widget.php:12
msgid "Akismet Widget"
msgstr "Akismet Widget"

#: class.akismet-widget.php:13
msgid "Display the number of spam comments Akismet has caught"
msgstr "Display the number of spam comments Akismet has caught"

#: class.akismet-widget.php:74
msgid "Title:"
msgstr "Title:"

#: class.akismet-widget.php:69 class.akismet-widget.php:90
msgid "Spam Blocked"
msgstr "Spam Blocked"

#: class.akismet-widget.php:102
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[1] "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"

#: class.akismet-admin.php:613
msgid "Akismet caught this comment as spam."
msgstr "Akismet caught this comment as spam."

#: class.akismet-admin.php:667
msgid "Comment status was changed to %s"
msgstr "Comment status was changed to %s"

#: class.akismet-admin.php:619
msgid "Akismet cleared this comment."
msgstr "Akismet cleared this comment."

#: class.akismet-admin.php:648
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet was unable to check this comment (response: %s) but will automatically retry later."

#: class.akismet-admin.php:673
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s changed the comment status to %2$s."

#: class.akismet-admin.php:626
msgid "%s reported this comment as spam."
msgstr "%s reported this comment as spam."

#: class.akismet-admin.php:634
msgid "%s reported this comment as not spam."
msgstr "%s reported this comment as not spam."

#: class.akismet-admin.php:641
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet caught this comment as spam during an automatic retry."

#: class.akismet-admin.php:644
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet cleared this comment during an automatic retry."

#: class.akismet.php:1348
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."

#: class.akismet.php:1348
msgid "Akismet %s requires WordPress %s or higher."
msgstr "Akismet %s requires WordPress %s or higher."

#: views/config.php:37 views/config.php:42
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Spam blocked"
msgstr[1] ""

#: views/config.php:35
msgid "Past six months"
msgstr "Past six months"

#: views/config.php:40
msgid "All time"
msgstr "All time"

#: views/config.php:45
msgid "Accuracy"
msgstr "Accuracy"

#: views/config.php:109
msgid "Show approved comments"
msgstr "Show approved comments"

#: views/config.php:122
msgid "Show the number of approved comments beside each comment author"
msgstr "Show the number of approved comments beside each comment author"

#: views/config.php:131
msgid "Akismet anti-spam strictness"
msgstr "Akismet anti-spam strictness"

#: views/config.php:132
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Silently discard the worst and most pervasive spam so I never see it."

#: views/config.php:133
msgid "Always put spam in the Spam folder for review."
msgstr "Always put spam in the Spam folder for review."

#: views/config.php:141
msgid "Spam in the <a href=\"%1$s\">spam folder</a> older than 1 day is deleted automatically."
msgid_plural "Spam in the <a href=\"%1$s\">spam folder</a> older than %2$d days is deleted automatically."
msgstr[0] "Spam in the <a href=\"%1$s\">spam folder</a> older than 1 day is deleted automatically."
msgstr[1] "Spam in the <a href=\"%1$s\">spam folder</a> older than %2$d days is deleted automatically."

#: views/config.php:135
msgid "Note:"
msgstr "Note:"

#: views/config.php:208
msgid "Cancelled"
msgstr "Cancelled"

#: views/config.php:176
msgid "Save Changes"
msgstr "Save Changes"

#: views/config.php:170
msgid "Disconnect this account"
msgstr "Disconnect this account"

#: views/config.php:210
msgid "Suspended"
msgstr "Suspended"

#: views/config.php:214
msgid "No Subscription Found"
msgstr "No Subscription Found"

#: views/config.php:212
msgid "Missing"
msgstr "Missing"

#: views/config.php:222
msgid "Next Billing Date"
msgstr "Next Billing Date"

#: views/config.php:216
msgid "Active"
msgstr "Active"

#: views/config.php:233
msgid "Upgrade"
msgstr "Upgrade"

#: views/config.php:233
msgid "Change"
msgstr "Change"

#: views/notice.php:23
msgid "Akismet has detected a problem."
msgstr "Akismet has detected a problem."

#: views/notice.php:24
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."

#: views/notice.php:31
msgid "Akismet Error Code: %s"
msgstr "Akismet Error Code: %s"

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:36
msgid "For more information: %s"
msgstr "For more information: %s"

#: views/notice.php:51
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."

#: views/notice.php:50
msgid "Network functions are disabled."
msgstr "Network functions are disabled."

#: views/notice.php:56
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."

#: views/notice.php:66
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."

#: views/notice.php:71 views/notice.php:81
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."

#: views/notice.php:76
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."

#: views/notice.php:80
msgid "There is a problem with your API key."
msgstr "There is a problem with your API key."

#: views/notice.php:112
msgid "The key you entered is invalid. Please double-check it."
msgstr "The key you entered is invalid. Please double-check it."

#: views/notice.php:126
msgid "You&#8217;re using your Akismet key on more sites than your Pro subscription allows."
msgstr "You&#8217;re using your Akismet key on more sites than your Pro subscription allows."

#: views/notice.php:133
msgid "You&#8217;re using Akismet on far too many sites for your Pro subscription."
msgstr "You&#8217;re using Akismet on far too many sites for your Pro subscription."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:36
msgid "Your subscription for %s is cancelled."
msgstr "Your subscription for %s is cancelled."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:44
msgid "Your subscription for %s is suspended."
msgstr "Your subscription for %s is suspended."

#: views/connect-jp.php:45
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "No worries! Get in touch and we&#8217;ll sort this out."

#: views/connect-jp.php:39
msgid "Contact Akismet support"
msgstr "Contact Akismet support"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Manually enter an API key"

#. Plugin URI of the plugin
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#. Author URI of the plugin
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Author of the plugin
msgid "Automattic"
msgstr "Automattic"

#: class.akismet-admin.php:153
msgid "Remove this URL"
msgstr "Remove this URL"

#: class.akismet-admin.php:91
msgid "Comment History"
msgstr "Comment History"