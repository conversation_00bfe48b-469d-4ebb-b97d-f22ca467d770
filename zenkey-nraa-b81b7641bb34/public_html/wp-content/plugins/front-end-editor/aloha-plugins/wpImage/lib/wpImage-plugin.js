// Generated by CoffeeScript 1.6.3
define(['aloha/plugin', 'ui/ui', 'ui/button', 'i18n!aloha/nls/i18n'], function(Plugin, Ui, Button, i18nCore) {
  var Aloha;
  Aloha = window.Aloha;
  return Plugin.create('wpImage', {
    init: function() {
      return Ui.adopt('wpImage', <PERSON><PERSON>, {
        'name': 'wpImage',
        'icon': 'aloha-button aloha-image-insert',
        'click': this.insert,
        'tooltip': i18nCore.t('floatingmenu.tab.insert'),
        'scope': 'Aloha.continuoustext'
      });
    },
    insert: function() {
      var instance;
      instance = new FrontEndEditor.fieldTypes.image_rich;
      return instance.start_editing();
    }
  });
});
