define({
	"floatingmenu.tab.table": "表格",
	"floatingmenu.tab.tablelayout": "表格布局",
	"deleterows.confirm": "你真的要删除所选择的行？",
	"deletecolumns.confirm": "你真的要删除所选择的列？",
	"deletetable.confirm": "你真的要删除此表格？",
	"Table": "表格",
	"button.createtable.tooltip": "插入表格",
	"button.addcolleft.tooltip": "左边插入一列",
	"button.addcolright.tooltip": "右边插入一列",
	"button.delcols.tooltip": "删除列",
	"button.addrowbefore.tooltip": "上方插入一行",
	"button.addrowafter.tooltip": "下方插入一行",
	"button.delrows.tooltip": "删除行",
	"button.caption.tooltip": "表格标题",
	"empty.caption": "标题",
	"button.removeFormat.tooltip": "删除格式",
	"button.removeFormat.text": "删除格式",
	"button.rowheader.tooltip": "格式化行作为表头",
	"button.columnheader.tooltip": "格式化列作为表头",
	"button.mergecells.tooltip": "合并单元格",
	"button.splitcells.tooltip": "拆分单元格",
	"table.label.target": "摘要",
	"table.sidebar.title": "表格",
	"table.mergeCells.notRectangular": "只有一个矩形选区可以合并",
	"table.addColumns.nonConsecutive": "请选择单个列或连续的列",
	"table.createTable.nestedTablesNoSupported": "抱歉，不支持嵌套表格"
});
