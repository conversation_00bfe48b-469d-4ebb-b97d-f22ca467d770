/*! This file is auto-generated */
!function(I){var k=window.wpNavMenu={options:{menuItemDepthPerLevel:30,globalMaxDepth:11,sortableItems:"> *",targetTolerance:0},menuList:void 0,targetList:void 0,menusChanged:!1,isRTL:!("undefined"==typeof isRtl||!isRtl),negateIfRTL:"undefined"!=typeof isRtl&&isRtl?-1:1,lastSearch:"",init:function(){k.menuList=I("#menu-to-edit"),k.targetList=k.menuList,this.jQueryExtensions(),this.attachMenuEditListeners(),this.attachQuickSearchListeners(),this.attachThemeLocationsListeners(),this.attachMenuSaveSubmitListeners(),this.attachTabsPanelListeners(),this.attachUnsavedChangesListener(),k.menuList.length&&this.initSortables(),menus.oneThemeLocationNoMenus&&I("#posttype-page").addSelectedToMenu(k.addMenuItemToBottom),this.initManageLocations(),this.initAccessibility(),this.initToggles(),this.initPreviewing()},jQueryExtensions:function(){I.fn.extend({menuItemDepth:function(){var e=k.isRTL?this.eq(0).css("margin-right"):this.eq(0).css("margin-left");return k.pxToDepth(e&&-1!=e.indexOf("px")?e.slice(0,-2):0)},updateDepthClass:function(t,n){return this.each(function(){var e=I(this);n=n||e.menuItemDepth(),I(this).removeClass("menu-item-depth-"+n).addClass("menu-item-depth-"+t)})},shiftDepthClass:function(i){return this.each(function(){var e=I(this),t=e.menuItemDepth(),n=t+i;e.removeClass("menu-item-depth-"+t).addClass("menu-item-depth-"+n),0===n&&e.find(".is-submenu").hide()})},childMenuItems:function(){var i=I();return this.each(function(){for(var e=I(this),t=e.menuItemDepth(),n=e.next(".menu-item");n.length&&n.menuItemDepth()>t;)i=i.add(n),n=n.next(".menu-item")}),i},shiftHorizontally:function(n){return this.each(function(){var e=I(this),t=e.menuItemDepth();e.moveHorizontally(t+n,t)})},moveHorizontally:function(a,s){return this.each(function(){var e=I(this),t=e.childMenuItems(),n=a-s,i=e.find(".is-submenu");e.updateDepthClass(a,s).updateParentMenuItemDBId(),t&&t.each(function(){var e=I(this),t=e.menuItemDepth();e.updateDepthClass(t+n,t).updateParentMenuItemDBId()}),0===a?i.hide():i.show()})},updateParentMenuItemDBId:function(){return this.each(function(){var e=I(this),t=e.find(".menu-item-data-parent-id"),n=parseInt(e.menuItemDepth(),10),e=e.prevAll(".menu-item-depth-"+(n-1)).first();0===n?t.val(0):t.val(e.find(".menu-item-data-db-id").val())})},hideAdvancedMenuItemFields:function(){return this.each(function(){var e=I(this);I(".hide-column-tog").not(":checked").each(function(){e.find(".field-"+I(this).val()).addClass("hidden-field")})})},addSelectedToMenu:function(a){return 0!==I("#menu-to-edit").length&&this.each(function(){var e=I(this),n={},t=menus.oneThemeLocationNoMenus&&0===e.find(".tabs-panel-active .categorychecklist li input:checked").length?e.find('#page-all li input[type="checkbox"]'):e.find(".tabs-panel-active .categorychecklist li input:checked"),i=/menu-item\[([^\]]*)/;if(a=a||k.addMenuItemToBottom,!t.length)return!1;e.find(".button-controls .spinner").addClass("is-active"),I(t).each(function(){var e=I(this),t=i.exec(e.attr("name")),t=void 0===t[1]?0:parseInt(t[1],10);this.className&&-1!=this.className.indexOf("add-to-top")&&(a=k.addMenuItemToTop),n[t]=e.closest("li").getItemData("add-menu-item",t)}),k.addItemToMenu(n,a,function(){t.prop("checked",!1),e.find(".button-controls .select-all").prop("checked",!1),e.find(".button-controls .spinner").removeClass("is-active")})})},getItemData:function(t,n){t=t||"menu-item";var i,a={},s=["menu-item-db-id","menu-item-object-id","menu-item-object","menu-item-parent-id","menu-item-position","menu-item-type","menu-item-title","menu-item-url","menu-item-description","menu-item-attr-title","menu-item-target","menu-item-classes","menu-item-xfn"];return(n=n||"menu-item"!=t?n:this.find(".menu-item-data-db-id").val())&&this.find("input").each(function(){var e;for(i=s.length;i--;)"menu-item"==t?e=s[i]+"["+n+"]":"add-menu-item"==t&&(e="menu-item["+n+"]["+s[i]+"]"),this.name&&e==this.name&&(a[s[i]]=this.value)}),a},setItemData:function(e,a,s){return a=a||"menu-item",(s=s||"menu-item"!=a?s:I(".menu-item-data-db-id",this).val())&&this.find("input").each(function(){var n,i=I(this);I.each(e,function(e,t){"menu-item"==a?n=e+"["+s+"]":"add-menu-item"==a&&(n="menu-item["+s+"]["+e+"]"),n==i.attr("name")&&i.val(t)})}),this}})},countMenuItems:function(e){return I(".menu-item-depth-"+e).length},moveMenuItem:function(e,t){var n,i,a=I("#menu-to-edit li"),s=a.length,m=e.parents("li.menu-item"),o=m.childMenuItems(),u=m.getItemData(),r=parseInt(m.menuItemDepth(),10),c=parseInt(m.index(),10),l=m.next(),d=l.childMenuItems(),h=parseInt(l.menuItemDepth(),10)+1,f=m.prev(),p=parseInt(f.menuItemDepth(),10),v=f.getItemData()["menu-item-db-id"];switch(t){case"up":if(i=c-1,0===c)break;0==i&&0!==r&&m.moveHorizontally(0,r),0!==p&&m.moveHorizontally(p,r),(o?n=m.add(o):m).detach().insertBefore(a.eq(i)).updateParentMenuItemDBId();break;case"down":if(o){if(n=m.add(o),(d=0!==(l=a.eq(n.length+c)).childMenuItems().length)&&(i=parseInt(l.menuItemDepth(),10)+1,m.moveHorizontally(i,r)),s===c+n.length)break;n.detach().insertAfter(a.eq(c+n.length)).updateParentMenuItemDBId()}else{if(0!==d.length&&m.moveHorizontally(h,r),s===c+1)break;m.detach().insertAfter(a.eq(c+1)).updateParentMenuItemDBId()}break;case"top":if(0===c)break;(o?n=m.add(o):m).detach().insertBefore(a.eq(0)).updateParentMenuItemDBId();break;case"left":if(0===r)break;m.shiftHorizontally(-1);break;case"right":if(0===c)break;if(u["menu-item-parent-id"]===v)break;m.shiftHorizontally(1)}e.trigger("focus"),k.registerChange(),k.refreshKeyboardAccessibility(),k.refreshAdvancedAccessibility()},initAccessibility:function(){var e=I("#menu-to-edit");k.refreshKeyboardAccessibility(),k.refreshAdvancedAccessibility(),e.on("mouseenter.refreshAccessibility focus.refreshAccessibility touchstart.refreshAccessibility",".menu-item",function(){k.refreshAdvancedAccessibilityOfItem(I(this).find("a.item-edit"))}),e.on("click","a.item-edit",function(){k.refreshAdvancedAccessibilityOfItem(I(this))}),e.on("click",".menus-move",function(){var e=I(this).data("dir");void 0!==e&&k.moveMenuItem(I(this).parents("li.menu-item").find("a.item-edit"),e)})},refreshAdvancedAccessibilityOfItem:function(e){var t,n,i,a,s,m,o,u,r,c,l,d,h;!0===I(e).data("needs_accessibility_refresh")&&(u=0===(o=(m=(s=I(e)).closest("li.menu-item").first()).menuItemDepth()),r=s.closest(".menu-item-handle").find(".menu-item-title").text(),c=parseInt(m.index(),10),h=u?o:parseInt(o-1,10),l=m.prevAll(".menu-item-depth-"+h).first().find(".menu-item-title").text(),d=m.prevAll(".menu-item-depth-"+o).first().find(".menu-item-title").text(),e=I("#menu-to-edit li").length,h=m.nextAll(".menu-item-depth-"+o).length,m.find(".field-move").toggle(1<e),0!==c&&(t=m.find(".menus-move-up")).attr("aria-label",menus.moveUp).css("display","inline"),0!==c&&u&&(t=m.find(".menus-move-top")).attr("aria-label",menus.moveToTop).css("display","inline"),c+1!==e&&0!==c&&(t=m.find(".menus-move-down")).attr("aria-label",menus.moveDown).css("display","inline"),0===c&&0!==h&&(t=m.find(".menus-move-down")).attr("aria-label",menus.moveDown).css("display","inline"),u||(t=m.find(".menus-move-left"),n=menus.outFrom.replace("%s",l),t.attr("aria-label",menus.moveOutFrom.replace("%s",l)).text(n).css("display","inline")),0!==c&&m.find(".menu-item-data-parent-id").val()!==m.prev().find(".menu-item-data-db-id").val()&&(t=m.find(".menus-move-right"),n=menus.under.replace("%s",d),t.attr("aria-label",menus.moveUnder.replace("%s",d)).text(n).css("display","inline")),a=u?(i=(u=I(".menu-item-depth-0")).index(m)+1,e=u.length,menus.menuFocus.replace("%1$s",r).replace("%2$d",i).replace("%3$d",e)):(o=(a=m.prevAll(".menu-item-depth-"+parseInt(o-1,10)).first()).find(".menu-item-data-db-id").val(),a=a.find(".menu-item-title").text(),o=I('.menu-item .menu-item-data-parent-id[value="'+o+'"]'),i=I(o.parents(".menu-item").get().reverse()).index(m)+1,menus.subMenuFocus.replace("%1$s",r).replace("%2$d",i).replace("%3$s",a)),s.attr("aria-label",a),s.data("needs_accessibility_refresh",!1))},refreshAdvancedAccessibility:function(){I(".menu-item-settings .field-move .menus-move").hide(),I("a.item-edit").data("needs_accessibility_refresh",!0),I(".menu-item-edit-active a.item-edit").each(function(){k.refreshAdvancedAccessibilityOfItem(this)})},refreshKeyboardAccessibility:function(){I("a.item-edit").off("focus").on("focus",function(){I(this).off("keydown").on("keydown",function(e){var t,n=I(this),i=n.parents("li.menu-item").getItemData();if((37==e.which||38==e.which||39==e.which||40==e.which)&&(n.off("keydown"),1!==I("#menu-to-edit li").length)){switch(t={38:"up",40:"down",37:"left",39:"right"},(t=I("body").hasClass("rtl")?{38:"up",40:"down",39:"left",37:"right"}:t)[e.which]){case"up":k.moveMenuItem(n,"up");break;case"down":k.moveMenuItem(n,"down");break;case"left":k.moveMenuItem(n,"left");break;case"right":k.moveMenuItem(n,"right")}return I("#edit-"+i["menu-item-db-id"]).trigger("focus"),!1}})})},initPreviewing:function(){I("#menu-to-edit").on("change input",".edit-menu-item-title",function(e){var t=I(e.currentTarget),e=t.val(),t=t.closest(".menu-item").find(".menu-item-title");e?t.text(e).removeClass("no-title"):t.text(wp.i18n._x("(no label)","missing menu item navigation label")).addClass("no-title")})},initToggles:function(){postboxes.add_postbox_toggles("nav-menus"),columns.useCheckboxesForHidden(),columns.checked=function(e){I(".field-"+e).removeClass("hidden-field")},columns.unchecked=function(e){I(".field-"+e).addClass("hidden-field")},k.menuList.hideAdvancedMenuItemFields(),I(".hide-postbox-tog").on("click",function(){var e=I(".accordion-container li.accordion-section").filter(":hidden").map(function(){return this.id}).get().join(",");I.post(ajaxurl,{action:"closed-postboxes",hidden:e,closedpostboxesnonce:jQuery("#closedpostboxesnonce").val(),page:"nav-menus"})})},initSortables:function(){var s,a,m,n,o,u,r,c,l,d,h=0,f=k.menuList.offset().left,p=I("body"),v=function(){if(!p[0].className)return 0;var e=p[0].className.match(/menu-max-depth-(\d+)/);return e&&e[1]?parseInt(e[1],10):0}();function g(e){n=e.placeholder.prev(".menu-item"),o=e.placeholder.next(".menu-item"),n[0]==e.item[0]&&(n=n.prev(".menu-item")),o[0]==e.item[0]&&(o=o.next(".menu-item")),u=n.length?n.offset().top+n.height():0,r=o.length?o.offset().top+o.height()/3:0,a=o.length?o.menuItemDepth():0,m=n.length?(e=n.menuItemDepth()+1)>k.options.globalMaxDepth?k.options.globalMaxDepth:e:0}function b(e,t){e.placeholder.updateDepthClass(t,h),h=t}0!==I("#menu-to-edit li").length&&I(".drag-instructions").show(),f+=k.isRTL?k.menuList.width():0,k.menuList.sortable({handle:".menu-item-handle",placeholder:"sortable-placeholder",items:k.options.sortableItems,start:function(e,t){var n,i;k.isRTL&&(t.item[0].style.right="auto"),l=t.item.children(".menu-item-transport"),s=t.item.menuItemDepth(),b(t,s),i=(t.item.next()[0]==t.placeholder[0]?t.item.next():t.item).childMenuItems(),l.append(i),n=l.outerHeight(),n+=0<n?+t.placeholder.css("margin-top").slice(0,-2):0,n+=t.helper.outerHeight(),c=n,t.placeholder.height(n-=2),d=s,i.each(function(){var e=I(this).menuItemDepth();d=d<e?e:d}),i=t.helper.find(".menu-item-handle").outerWidth(),i+=k.depthToPx(d-s),t.placeholder.width(i-=2),(i=t.placeholder.next(".menu-item")).css("margin-top",c+"px"),t.placeholder.detach(),I(this).sortable("refresh"),t.item.after(t.placeholder),i.css("margin-top",0),g(t)},stop:function(e,t){var n=h-s,i=l.children().insertAfter(t.item),a=t.item.find(".item-title .is-submenu");0<h?a.show():a.hide(),0!=n&&(t.item.updateDepthClass(h),i.shiftDepthClass(n),function(e){var t,n=v;if(0!==e){if(0<e)v<(t=d+e)&&(n=t);else if(e<0&&d==v)for(;!I(".menu-item-depth-"+n,k.menuList).length&&0<n;)n--;p.removeClass("menu-max-depth-"+v).addClass("menu-max-depth-"+n),v=n}}(n)),k.registerChange(),t.item.updateParentMenuItemDBId(),t.item[0].style.top=0,k.isRTL&&(t.item[0].style.left="auto",t.item[0].style.right=0),k.refreshKeyboardAccessibility(),k.refreshAdvancedAccessibility()},change:function(e,t){t.placeholder.parent().hasClass("menu")||(n.length?n.after(t.placeholder):k.menuList.prepend(t.placeholder)),g(t)},sort:function(e,t){var n=t.helper.offset(),i=k.isRTL?n.left+t.helper.width():n.left,i=k.negateIfRTL*k.pxToDepth(i-f);m<i||n.top<u-k.options.targetTolerance?i=m:i<a&&(i=a),i!=h&&b(t,i),r&&n.top+c>r&&(o.after(t.placeholder),g(t),I(this).sortable("refreshPositions"))}})},initManageLocations:function(){I("#menu-locations-wrap form").on("submit",function(){window.onbeforeunload=null}),I(".menu-location-menus select").on("change",function(){var e=I(this).closest("tr").find(".locations-edit-menu-link");I(this).find("option:selected").data("orig")?e.show():e.hide()})},attachMenuEditListeners:function(){var t=this;I("#update-nav-menu").on("click",function(e){if(e.target&&e.target.className)return-1!=e.target.className.indexOf("item-edit")?t.eventOnClickEditLink(e.target):-1!=e.target.className.indexOf("menu-save")?t.eventOnClickMenuSave(e.target):-1!=e.target.className.indexOf("menu-delete")?t.eventOnClickMenuDelete(e.target):-1!=e.target.className.indexOf("item-delete")?t.eventOnClickMenuItemDelete(e.target):-1!=e.target.className.indexOf("item-cancel")?t.eventOnClickCancelLink(e.target):void 0}),I("#menu-name").on("input",_.debounce(function(){var e=I(document.getElementById("menu-name")),t=e.val();t&&t.replace(/\s+/,"")?e.parent().removeClass("form-invalid"):e.parent().addClass("form-invalid")},500)),I('#add-custom-links input[type="text"]').on("keypress",function(e){I("#customlinkdiv").removeClass("form-invalid"),13===e.keyCode&&(e.preventDefault(),I("#submit-customlinkdiv").trigger("click"))})},attachMenuSaveSubmitListeners:function(){I("#update-nav-menu").on("submit",function(){var e=I("#update-nav-menu").serializeArray();I('[name="nav-menu-data"]').val(JSON.stringify(e))})},attachThemeLocationsListeners:function(){var e=I("#nav-menu-theme-locations"),t={action:"menu-locations-save"};t["menu-settings-column-nonce"]=I("#menu-settings-column-nonce").val(),e.find('input[type="submit"]').on("click",function(){return e.find("select").each(function(){t[this.name]=I(this).val()}),e.find(".spinner").addClass("is-active"),I.post(ajaxurl,t,function(){e.find(".spinner").removeClass("is-active")}),!1})},attachQuickSearchListeners:function(){var t;I("#nav-menu-meta").on("submit",function(e){e.preventDefault()}),I("#nav-menu-meta").on("input",".quick-search",function(){var e=I(this);e.attr("autocomplete","off"),t&&clearTimeout(t),t=setTimeout(function(){k.updateQuickSearchResults(e)},500)}).on("blur",".quick-search",function(){k.lastSearch=""})},updateQuickSearchResults:function(e){var t,n,i=e.val();i.length<2||k.lastSearch==i||(k.lastSearch=i,t=e.parents(".tabs-panel"),n={action:"menu-quick-search","response-format":"markup",menu:I("#menu").val(),"menu-settings-column-nonce":I("#menu-settings-column-nonce").val(),q:i,type:e.attr("name")},I(".spinner",t).addClass("is-active"),I.post(ajaxurl,n,function(e){k.processQuickSearchQueryResponse(e,n,t)}))},addCustomLink:function(e){var t=I("#custom-menu-item-url").val().toString(),n=I("#custom-menu-item-name").val();if(""!==t&&(t=t.trim()),e=e||k.addMenuItemToBottom,""===t||"https://"==t||"http://"==t)return I("#customlinkdiv").addClass("form-invalid"),!1;I(".customlinkdiv .spinner").addClass("is-active"),this.addLinkToMenu(t,n,e,function(){I(".customlinkdiv .spinner").removeClass("is-active"),I("#custom-menu-item-name").val("").trigger("blur"),I("#custom-menu-item-url").val("").attr("placeholder","https://")})},addLinkToMenu:function(e,t,n,i){n=n||k.addMenuItemToBottom,k.addItemToMenu({"-1":{"menu-item-type":"custom","menu-item-url":e,"menu-item-title":t}},n,i=i||function(){})},addItemToMenu:function(e,n,i){var a,t=I("#menu").val(),s=I("#menu-settings-column-nonce").val();n=n||function(){},i=i||function(){},a={action:"add-menu-item",menu:t,"menu-settings-column-nonce":s,"menu-item":e},I.post(ajaxurl,a,function(e){var t=I("#menu-instructions");e=(e=e||"").toString().trim(),n(e,a),I("li.pending").hide().fadeIn("slow"),I(".drag-instructions").show(),!t.hasClass("menu-instructions-inactive")&&t.siblings().length&&t.addClass("menu-instructions-inactive"),i()})},addMenuItemToBottom:function(e){e=I(e);e.hideAdvancedMenuItemFields().appendTo(k.targetList),k.refreshKeyboardAccessibility(),k.refreshAdvancedAccessibility(),I(document).trigger("menu-item-added",[e])},addMenuItemToTop:function(e){e=I(e);e.hideAdvancedMenuItemFields().prependTo(k.targetList),k.refreshKeyboardAccessibility(),k.refreshAdvancedAccessibility(),I(document).trigger("menu-item-added",[e])},attachUnsavedChangesListener:function(){I("#menu-management input, #menu-management select, #menu-management, #menu-management textarea, .menu-location-menus select").on("change",function(){k.registerChange()}),0!==I("#menu-to-edit").length||0!==I(".menu-location-menus select").length?window.onbeforeunload=function(){if(k.menusChanged)return wp.i18n.__("The changes you made will be lost if you navigate away from this page.")}:I("#menu-settings-column").find("input,select").end().find("a").attr("href","#").off("click")},registerChange:function(){k.menusChanged=!0},attachTabsPanelListeners:function(){I("#menu-settings-column").on("click",function(e){var t,n,i,a,s=I(e.target);if(s.hasClass("nav-tab-link"))n=s.data("type"),i=s.parents(".accordion-section-content").first(),I("input",i).prop("checked",!1),I(".tabs-panel-active",i).removeClass("tabs-panel-active").addClass("tabs-panel-inactive"),I("#"+n,i).removeClass("tabs-panel-inactive").addClass("tabs-panel-active"),I(".tabs",i).removeClass("tabs"),s.parent().addClass("tabs"),I(".quick-search",i).trigger("focus"),i.find(".tabs-panel-active .menu-item-title").length?i.removeClass("has-no-menu-item"):i.addClass("has-no-menu-item"),e.preventDefault();else if(s.hasClass("select-all"))(t=s.closest(".button-controls").data("items-type"))&&((a=I("#"+t+" .tabs-panel-active .menu-item-title input")).length!==a.filter(":checked").length||s.is(":checked")?s.is(":checked")&&a.prop("checked",!0):a.prop("checked",!1));else if(s.hasClass("menu-item-checkbox"))(t=s.closest(".tabs-panel-active").parent().attr("id"))&&(a=I("#"+t+" .tabs-panel-active .menu-item-title input"),t=I('.button-controls[data-items-type="'+t+'"] .select-all'),a.length!==a.filter(":checked").length||t.is(":checked")?t.is(":checked")&&t.prop("checked",!1):t.prop("checked",!0));else if(s.hasClass("submit-add-to-menu"))return k.registerChange(),e.target.id&&"submit-customlinkdiv"==e.target.id?k.addCustomLink(k.addMenuItemToBottom):e.target.id&&-1!=e.target.id.indexOf("submit-")&&I("#"+e.target.id.replace(/submit-/,"")).addSelectedToMenu(k.addMenuItemToBottom),!1}),I("#nav-menu-meta").on("click","a.page-numbers",function(){var n=I(this).closest(".inside");return I.post(ajaxurl,this.href.replace(/.*\?/,"").replace(/action=([^&]*)/,"")+"&action=menu-get-metabox",function(e){var t=JSON.parse(e);-1!==e.indexOf("replace-id")&&(e=document.getElementById(t["replace-id"]),t.markup&&e&&n.html(t.markup))}),!1})},eventOnClickEditLink:function(e){var t,n,e=/#(.*)$/.exec(e.href);if(e&&e[1]&&0!==(n=(t=I("#"+e[1])).parent()).length)return n.hasClass("menu-item-edit-inactive")?(t.data("menu-item-data")||t.data("menu-item-data",t.getItemData()),t.slideDown("fast"),n.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active")):(t.slideUp("fast"),n.removeClass("menu-item-edit-active").addClass("menu-item-edit-inactive")),!1},eventOnClickCancelLink:function(e){var t=I(e).closest(".menu-item-settings"),e=I(e).closest(".menu-item");return e.removeClass("menu-item-edit-active").addClass("menu-item-edit-inactive"),t.setItemData(t.data("menu-item-data")).hide(),e.find(".menu-item-title").text(t.data("menu-item-data")["menu-item-title"]),!1},eventOnClickMenuSave:function(){var e="",t=I("#menu-name"),n=t.val();return n&&n.replace(/\s+/,"")?(I("#nav-menu-theme-locations select").each(function(){e+='<input type="hidden" name="'+this.name+'" value="'+I(this).val()+'" />'}),I("#update-nav-menu").append(e),k.menuList.find(".menu-item-data-position").val(function(e){return e+1}),!(window.onbeforeunload=null)):(t.parent().addClass("form-invalid"),!1)},eventOnClickMenuDelete:function(){return!!window.confirm(wp.i18n.__("You are about to permanently delete this menu.\n'Cancel' to stop, 'OK' to delete."))&&!(window.onbeforeunload=null)},eventOnClickMenuItemDelete:function(e){e=parseInt(e.id.replace("delete-",""),10);return k.removeMenuItem(I("#menu-item-"+e)),k.registerChange(),!1},processQuickSearchQueryResponse:function(e,t,n){var i,a,s,m={},o=document.getElementById("nav-menu-meta"),u=/menu-item[(\[^]\]*/,r=I("<div>").html(e).find("li"),c=n.closest(".accordion-section-content"),e=c.find(".button-controls .select-all");if(!r.length)return I(".categorychecklist",n).html("<li><p>"+wp.i18n.__("No results found.")+"</p></li>"),I(".spinner",n).removeClass("is-active"),void c.addClass("has-no-menu-item");r.each(function(){if(s=I(this),(i=u.exec(s.html()))&&i[1]){for(a=i[1];o.elements["menu-item["+a+"][menu-item-type]"]||m[a];)a--;m[a]=!0,a!=i[1]&&s.html(s.html().replace(new RegExp("menu-item\\["+i[1]+"\\]","g"),"menu-item["+a+"]"))}}),I(".categorychecklist",n).html(r),I(".spinner",n).removeClass("is-active"),c.removeClass("has-no-menu-item"),e.is(":checked")&&e.prop("checked",!1)},removeMenuItem:function(t){var n=t.childMenuItems();I(document).trigger("menu-removing-item",[t]),t.addClass("deleting").animate({opacity:0,height:0},350,function(){var e=I("#menu-instructions");t.remove(),n.shiftDepthClass(-1).updateParentMenuItemDBId(),0===I("#menu-to-edit li").length&&(I(".drag-instructions").hide(),e.removeClass("menu-instructions-inactive")),k.refreshAdvancedAccessibility()})},depthToPx:function(e){return e*k.options.menuItemDepthPerLevel},pxToDepth:function(e){return Math.floor(e/k.options.menuItemDepthPerLevel)}};I(document).ready(function(){wpNavMenu.init(),I(".menu-edit a, .menu-edit button, .menu-edit input, .menu-edit textarea, .menu-edit select").on("focus",function(){var e,t,n;783<=window.innerWidth&&(e=I("#nav-menu-footer").height()+20,0<(t=I(this).offset().top-(I(window).scrollTop()+I(window).height()-I(this).height()))&&(t=0),(t*=-1)<e&&(n=I(document).scrollTop(),I(document).scrollTop(n+(e-t))))})})}(jQuery);