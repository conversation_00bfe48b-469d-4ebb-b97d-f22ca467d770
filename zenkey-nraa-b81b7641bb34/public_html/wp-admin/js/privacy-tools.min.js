/*! This file is auto-generated */
jQuery(document).ready(function(v){var r,h=wp.i18n.__;function w(e,t){e.children().addClass("hidden"),e.children("."+t).removeClass("hidden")}function x(e){e.removeClass("has-request-results"),e.next().hasClass("request-results")&&e.next().remove()}function y(e,t,a,o){var s="",n="request-results";x(e),o.length&&(v.each(o,function(e,t){s=s+"<li>"+t+"</li>"}),s="<ul>"+s+"</ul>"),e.addClass("has-request-results"),e.hasClass("status-request-confirmed")&&(n+=" status-request-confirmed"),e.hasClass("status-request-failed")&&(n+=" status-request-failed"),e.after(function(){return'<tr class="'+n+'"><th colspan="5"><div class="notice inline notice-alt '+t+'"><p>'+a+"</p>"+s+"</div></td></tr>"})}v(".export-personal-data-handle").on("click",function(e){var t=v(this),n=t.parents(".export-personal-data"),r=t.parents("tr"),a=r.find(".export-progress"),i=t.parents(".row-actions"),c=n.data("request-id"),d=n.data("nonce"),l=n.data("exporters-count"),u=!!n.data("send-as-email");function p(e){var t=h("An error occurred while attempting to export personal data.");w(n,"export-personal-data-failed"),e&&y(r,"notice-error",t,[e]),setTimeout(function(){i.removeClass("processing")},500)}function m(e){e=Math.round(100*(0<l?e/l:0)).toString()+"%";a.html(e)}e.preventDefault(),e.stopPropagation(),i.addClass("processing"),n.trigger("blur"),x(r),m(0),w(n,"export-personal-data-processing"),function t(o,s){v.ajax({url:window.ajaxurl,data:{action:"wp-privacy-export-personal-data",exporter:o,id:c,page:s,security:d,sendAsEmail:u},method:"post"}).done(function(e){var a=e.data;e.success?a.done?(m(o),o<l?setTimeout(t(o+1,1)):setTimeout(function(){var e,t;e=a.url,t=h("This user&#8217;s personal data export link was sent."),void 0!==e&&(t=h("This user&#8217;s personal data export file was downloaded.")),w(n,"export-personal-data-success"),y(r,"notice-success",t,[]),void 0!==e?window.location=e:u||p(h("No personal data export file was generated.")),setTimeout(function(){i.removeClass("processing")},500)},500)):setTimeout(t(o,s+1)):setTimeout(function(){p(e.data)},500)}).fail(function(e,t,a){setTimeout(function(){p(a)},500)})}(1,1)}),v(".remove-personal-data-handle").on("click",function(e){var t=v(this),n=t.parents(".remove-personal-data"),r=t.parents("tr"),a=r.find(".erasure-progress"),i=t.parents(".row-actions"),c=n.data("request-id"),d=n.data("nonce"),l=n.data("erasers-count"),u=!1,p=!1,m=[];function f(){var e=h("An error occurred while attempting to find and erase personal data.");w(n,"remove-personal-data-failed"),y(r,"notice-error",e,[]),setTimeout(function(){i.removeClass("processing")},500)}function g(e){e=Math.round(100*(0<l?e/l:0)).toString()+"%";a.html(e)}e.preventDefault(),e.stopPropagation(),i.addClass("processing"),n.trigger("blur"),x(r),g(0),w(n,"remove-personal-data-processing"),function a(o,s){v.ajax({url:window.ajaxurl,data:{action:"wp-privacy-erase-personal-data",eraser:o,id:c,page:s,security:d},method:"post"}).done(function(e){var t=e.data;e.success?(t.items_removed&&(u=u||t.items_removed),t.items_retained&&(p=p||t.items_retained),t.messages&&(m=m.concat(t.messages)),t.done?(g(o),o<l?setTimeout(a(o+1,1)):setTimeout(function(){var e,t;e=h("No personal data was found for this user."),t="notice-success",w(n,"remove-personal-data-success"),!1===u?!1===p?e=h("No personal data was found for this user."):(e=h("Personal data was found for this user but was not erased."),t="notice-warning"):!1===p?e=h("All of the personal data found for this user was erased."):(e=h("Personal data was found for this user but some of the personal data found was not erased."),t="notice-warning"),y(r,t,e,m),setTimeout(function(){i.removeClass("processing")},500)},500)):setTimeout(a(o,s+1))):setTimeout(function(){f()},500)}).fail(function(){setTimeout(function(){f()},500)})}(1,1)}),v(document).on("click",function(e){var t,a,e=v(e.target),o=e.siblings(".success");if(clearTimeout(r),e.is("button.privacy-text-copy")&&(t=e.closest(".privacy-settings-accordion-panel")).length)try{var s=document.documentElement.scrollTop,n=document.body.scrollTop;window.getSelection().removeAllRanges(),a=document.createRange(),t.addClass("hide-privacy-policy-tutorial"),a.selectNodeContents(t[0]),window.getSelection().addRange(a),document.execCommand("copy"),t.removeClass("hide-privacy-policy-tutorial"),window.getSelection().removeAllRanges(),0<s&&s!==document.documentElement.scrollTop?document.documentElement.scrollTop=s:0<n&&n!==document.body.scrollTop&&(document.body.scrollTop=n),o.addClass("visible"),wp.a11y.speak(h("The suggested policy text has been copied to your clipboard.")),r=setTimeout(function(){o.removeClass("visible")},3e3)}catch(e){}}),v("body.options-privacy-php label[for=create-page]").on("click",function(e){e.preventDefault(),v("input#create-page").trigger("focus")}),v(".privacy-settings-accordion").on("click",".privacy-settings-accordion-trigger",function(){"true"===v(this).attr("aria-expanded")?(v(this).attr("aria-expanded","false"),v("#"+v(this).attr("aria-controls")).attr("hidden",!0)):(v(this).attr("aria-expanded","true"),v("#"+v(this).attr("aria-controls")).attr("hidden",!1))})});