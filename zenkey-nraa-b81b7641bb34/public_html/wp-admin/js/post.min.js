/*! This file is auto-generated */
window.makeSlugeditClickable=window.editPermalink=function(){},window.wp=window.wp||{},function(s){var t=!1,a=wp.i18n.__;window.commentsBox={st:0,get:function(t,e){var i=this.st;return this.st+=e=e||20,this.total=t,s("#commentsdiv .spinner").addClass("is-active"),e={action:"get-comments",mode:"single",_ajax_nonce:s("#add_comment_nonce").val(),p:s("#post_ID").val(),start:i,number:e},s.post(ajaxurl,e,function(t){return t=wpAjax.parseAjaxResponse(t),s("#commentsdiv .widefat").show(),s("#commentsdiv .spinner").removeClass("is-active"),"object"==typeof t&&t.responses[0]?(s("#the-comment-list").append(t.responses[0].data),theList=theExtraList=null,s("a[className*=':']").off(),void(commentsBox.st>commentsBox.total?s("#show-comments").hide():s("#show-comments").show().children("a").text(a("Show more comments")))):void(1!=t?s("#the-comment-list").append('<tr><td colspan="2">'+wpAjax.broken+"</td></tr>"):s("#show-comments").text(a("No more comments found.")))}),!1},load:function(t){this.st=jQuery("#the-comment-list tr.comment:visible").length,this.get(t)}},window.WPSetThumbnailHTML=function(t){s(".inside","#postimagediv").html(t)},window.WPSetThumbnailID=function(t){var e=s('input[value="_thumbnail_id"]',"#list-table");0<e.length&&s("#meta\\["+e.attr("id").match(/[0-9]+/)+"\\]\\[value\\]").text(t)},window.WPRemoveThumbnail=function(t){s.post(ajaxurl,{action:"set-post-thumbnail",post_id:s("#post_ID").val(),thumbnail_id:-1,_ajax_nonce:t,cookie:encodeURIComponent(document.cookie)},function(t){"0"==t?alert(a("Could not set that as the thumbnail image. Try a different attachment.")):WPSetThumbnailHTML(t)})},s(document).on("heartbeat-send.refresh-lock",function(t,e){var i=s("#active_post_lock").val(),a=s("#post_ID").val(),n={};a&&s("#post-lock-dialog").length&&(n.post_id=a,i&&(n.lock=i),e["wp-refresh-post-lock"]=n)}).on("heartbeat-tick.refresh-lock",function(t,e){var i,a;e["wp-refresh-post-lock"]&&((i=e["wp-refresh-post-lock"]).lock_error?(a=s("#post-lock-dialog")).length&&!a.is(":visible")&&(wp.autosave&&(s(document).one("heartbeat-tick",function(){wp.autosave.server.suspend(),a.removeClass("saving").addClass("saved"),s(window).off("beforeunload.edit-post")}),a.addClass("saving"),wp.autosave.server.triggerSave()),i.lock_error.avatar_src&&(e=s("<img />",{class:"avatar avatar-64 photo",width:64,height:64,alt:"",src:i.lock_error.avatar_src,srcset:i.lock_error.avatar_src_2x?i.lock_error.avatar_src_2x+" 2x":void 0}),a.find("div.post-locked-avatar").empty().append(e)),a.show().find(".currently-editing").text(i.lock_error.text),a.find(".wp-tab-first").trigger("focus")):i.new_lock&&s("#active_post_lock").val(i.new_lock))}).on("before-autosave.update-post-slug",function(){t=document.activeElement&&"title"===document.activeElement.id}).on("after-autosave.update-post-slug",function(){s("#edit-slug-box > *").length||t||s.post(ajaxurl,{action:"sample-permalink",post_id:s("#post_ID").val(),new_title:s("#title").val(),samplepermalinknonce:s("#samplepermalinknonce").val()},function(t){"-1"!=t&&s("#edit-slug-box").html(t)})})}(jQuery),function(n){var s,t;function i(){s=!1,window.clearTimeout(t),t=window.setTimeout(function(){s=!0},3e5)}n(document).on("heartbeat-send.wp-refresh-nonces",function(t,e){var i,a=n("#wp-auth-check-wrap");(s||a.length&&!a.hasClass("hidden"))&&(i=n("#post_ID").val())&&n("#_wpnonce").val()&&(e["wp-refresh-post-nonces"]={post_id:i})}).on("heartbeat-tick.wp-refresh-nonces",function(t,e){e=e["wp-refresh-post-nonces"];e&&(i(),e.replace&&n.each(e.replace,function(t,e){n("#"+t).val(e)}),e.heartbeatNonce&&(window.heartbeatSettings.nonce=e.heartbeatNonce))}).ready(function(){i()})}(jQuery),jQuery(document).ready(function(u){var p,e,i,a,n,s,o,l,r,t,c,d,h=u("#content"),f=u(document),v=u("#post_ID").val()||0,m=u("#submitpost"),g=!0,w=u("#post-visibility-select"),b=u("#timestampdiv"),k=u("#post-status-select"),_=!!window.navigator.platform&&-1!==window.navigator.platform.indexOf("Mac"),y=new ClipboardJS(".copy-attachment-url.edit-media"),x=wp.i18n.__,C=wp.i18n._x;function D(t){c.hasClass("wp-editor-expand")||(r?o.theme.resizeTo(null,l+t.pageY):h.height(Math.max(50,l+t.pageY)),t.preventDefault())}function j(){var t;c.hasClass("wp-editor-expand")||(t=r?(o.focus(),((t=parseInt(u("#wp-content-editor-container .mce-toolbar-grp").height(),10))<10||200<t)&&(t=30),parseInt(u("#content_ifr").css("height"),10)+t-28):(h.trigger("focus"),parseInt(h.css("height"),10)),f.off(".wp-editor-resize"),t&&50<t&&t<5e3&&setUserSetting("ed_size",t))}postboxes.add_postbox_toggles(pagenow),window.name="",u("#post-lock-dialog .notification-dialog").on("keydown",function(t){var e;9==t.which&&((e=u(t.target)).hasClass("wp-tab-first")&&t.shiftKey?(u(this).find(".wp-tab-last").trigger("focus"),t.preventDefault()):e.hasClass("wp-tab-last")&&!t.shiftKey&&(u(this).find(".wp-tab-first").trigger("focus"),t.preventDefault()))}).filter(":visible").find(".wp-tab-first").trigger("focus"),wp.heartbeat&&u("#post-lock-dialog").length&&wp.heartbeat.interval(15),i=m.find(":submit, a.submitdelete, #post-preview").on("click.edit-post",function(t){var e=u(this);e.hasClass("disabled")?t.preventDefault():e.hasClass("submitdelete")||e.is("#post-preview")||u("form#post").off("submit.edit-post").on("submit.edit-post",function(t){if(!t.isDefaultPrevented()){if(wp.autosave&&wp.autosave.server.suspend(),"undefined"!=typeof commentReply){if(!commentReply.discardCommentChanges())return!1;commentReply.close()}g=!1,u(window).off("beforeunload.edit-post"),i.addClass("disabled"),("publish"===e.attr("id")?m.find("#major-publishing-actions .spinner"):m.find("#minor-publishing .spinner")).addClass("is-active")}})}),u("#post-preview").on("click.post-preview",function(t){var e=u(this),i=u("form#post"),a=u("input#wp-preview"),n=e.attr("target")||"wp-preview",s=navigator.userAgent.toLowerCase();t.preventDefault(),e.hasClass("disabled")||(wp.autosave&&wp.autosave.server.tempBlockSave(),a.val("dopreview"),i.attr("target",n).trigger("submit").attr("target",""),-1!==s.indexOf("safari")&&-1===s.indexOf("chrome")&&i.attr("action",function(t,e){return e+"?t="+(new Date).getTime()}),a.val(""))}),u("#title").on("keydown.editor-focus",function(t){var e;if(9===t.keyCode&&!t.ctrlKey&&!t.altKey&&!t.shiftKey){if((e="undefined"!=typeof tinymce&&tinymce.get("content"))&&!e.isHidden())e.focus();else{if(!h.length)return;h.trigger("focus")}t.preventDefault()}}),u("#auto_draft").val()&&u("#title").on("blur",function(){var t;this.value&&!u("#edit-slug-box > *").length&&(u("form#post").one("submit",function(){t=!0}),window.setTimeout(function(){!t&&wp.autosave&&wp.autosave.server.triggerSave()},200))}),f.on("autosave-disable-buttons.edit-post",function(){i.addClass("disabled")}).on("autosave-enable-buttons.edit-post",function(){wp.heartbeat&&wp.heartbeat.hasConnectionError()||i.removeClass("disabled")}).on("before-autosave.edit-post",function(){u(".autosave-message").text(x("Saving Draft\u2026"))}).on("after-autosave.edit-post",function(t,e){u(".autosave-message").text(e.message),u(document.body).hasClass("post-new-php")&&u(".submitbox .submitdelete").show()}),u(window).on("beforeunload.edit-post",function(t){var e=window.tinymce&&window.tinymce.get("content"),i=!1;if(wp.autosave?i=wp.autosave.server.postChanged():e&&(i=!e.isHidden()&&e.isDirty()),i)return t.preventDefault(),x("The changes you made will be lost if you navigate away from this page.")}).on("unload.edit-post",function(t){if(g&&(!t.target||"#document"==t.target.nodeName)){var e=u("#post_ID").val(),t=u("#active_post_lock").val();if(e&&t){t={action:"wp-remove-post-lock",_wpnonce:u("#_wpnonce").val(),post_ID:e,active_post_lock:t};if(window.FormData&&window.navigator.sendBeacon){var i=new window.FormData;if(u.each(t,function(t,e){i.append(t,e)}),window.navigator.sendBeacon(ajaxurl,i))return}u.post({async:!1,data:t,url:ajaxurl})}}}),u("#tagsdiv-post_tag").length?window.tagBox&&window.tagBox.init():u(".meta-box-sortables").children("div.postbox").each(function(){if(0===this.id.indexOf("tagsdiv-"))return window.tagBox&&window.tagBox.init(),!1}),u(".categorydiv").each(function(){var t,n,e,i=u(this).attr("id").split("-");i.shift(),n=i.join("-"),e="category"==n?"cats":n+"_tab",u("a","#"+n+"-tabs").on("click",function(t){t.preventDefault();t=u(this).attr("href");u(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),u("#"+n+"-tabs").siblings(".tabs-panel").hide(),u(t).show(),"#"+n+"-all"==t?deleteUserSetting(e):setUserSetting(e,"pop")}),getUserSetting(e)&&u('a[href="#'+n+'-pop"]',"#"+n+"-tabs").trigger("click"),u("#new"+n).one("focus",function(){u(this).val("").removeClass("form-input-tip")}),u("#new"+n).on("keypress",function(t){13===t.keyCode&&(t.preventDefault(),u("#"+n+"-add-submit").trigger("click"))}),u("#"+n+"-add-submit").on("click",function(){u("#new"+n).trigger("focus")}),t=function(t){return!!u("#new"+n).val()&&(t.data+="&"+u(":checked","#"+n+"checklist").serialize(),u("#"+n+"-add-submit").prop("disabled",!0),t)},i=function(t,e){var i,a=u("#new"+n+"_parent");u("#"+n+"-add-submit").prop("disabled",!1),"undefined"!=e.parsed.responses[0]&&(i=e.parsed.responses[0].supplemental.newcat_parent)&&(a.before(i),a.remove())},u("#"+n+"checklist").wpList({alt:"",response:n+"-ajax-response",addBefore:t,addAfter:i}),u("#"+n+"-add-toggle").on("click",function(t){t.preventDefault(),u("#"+n+"-adder").toggleClass("wp-hidden-children"),u('a[href="#'+n+'-all"]',"#"+n+"-tabs").click(),u("#new"+n).trigger("focus")}),u("#"+n+"checklist, #"+n+"checklist-pop").on("click",'li.popular-category > label input[type="checkbox"]',function(){var t=u(this),e=t.is(":checked"),i=t.val();i&&t.parents("#taxonomy-"+n).length&&u("#in-"+n+"-"+i+", #in-popular-"+n+"-"+i).prop("checked",e)})}),u("#postcustom").length&&u("#the-list").wpList({addBefore:function(t){return t.data+="&post_id="+u("#post_ID").val(),t},addAfter:function(){u("table#list-table").show()}}),u("#submitdiv").length&&(p=u("#timestamp").html(),e=u("#post-visibility-display").html(),a=function(){"public"!=w.find("input:radio:checked").val()?(u("#sticky").prop("checked",!1),u("#sticky-span").hide()):u("#sticky-span").show(),"password"!=w.find("input:radio:checked").val()?u("#password-span").hide():u("#password-span").show()},n=function(){if(!b.length)return!0;var t,e=u("#post_status"),i=u('option[value="publish"]',e),a=u("#aa").val(),n=u("#mm").val(),s=u("#jj").val(),o=u("#hh").val(),l=u("#mn").val(),r=new Date(a,n-1,s,o,l),c=new Date(u("#hidden_aa").val(),u("#hidden_mm").val()-1,u("#hidden_jj").val(),u("#hidden_hh").val(),u("#hidden_mn").val()),d=new Date(u("#cur_aa").val(),u("#cur_mm").val()-1,u("#cur_jj").val(),u("#cur_hh").val(),u("#cur_mn").val());return r.getFullYear()!=a||1+r.getMonth()!=n||r.getDate()!=s||r.getMinutes()!=l?(b.find(".timestamp-wrap").addClass("form-invalid"),!1):(b.find(".timestamp-wrap").removeClass("form-invalid"),d<r&&"future"!=u("#original_post_status").val()?(t=x("Schedule for:"),u("#publish").val(C("Schedule","post action/button label"))):r<=d&&"publish"!=u("#original_post_status").val()?(t=x("Publish on:"),u("#publish").val(x("Publish"))):(t=x("Published on:"),u("#publish").val(x("Update"))),c.toUTCString()==r.toUTCString()?u("#timestamp").html(p):u("#timestamp").html("\n"+t+" <b>"+x("%1$s %2$s, %3$s at %4$s:%5$s").replace("%1$s",u('option[value="'+n+'"]',"#mm").attr("data-text")).replace("%2$s",parseInt(s,10)).replace("%3$s",a).replace("%4$s",("00"+o).slice(-2)).replace("%5$s",("00"+l).slice(-2))+"</b> "),"private"==w.find("input:radio:checked").val()?(u("#publish").val(x("Update")),0===i.length?e.append('<option value="publish">'+x("Privately Published")+"</option>"):i.html(x("Privately Published")),u('option[value="publish"]',e).prop("selected",!0),u("#misc-publishing-actions .edit-post-status").hide()):("future"==u("#original_post_status").val()||"draft"==u("#original_post_status").val()?i.length&&(i.remove(),e.val(u("#hidden_post_status").val())):i.html(x("Published")),e.is(":hidden")&&u("#misc-publishing-actions .edit-post-status").show()),u("#post-status-display").text(wp.sanitize.stripTagsAndEncodeText(u("option:selected",e).text())),"private"==u("option:selected",e).val()||"publish"==u("option:selected",e).val()?u("#save-post").hide():(u("#save-post").show(),"pending"==u("option:selected",e).val()?u("#save-post").show().val(x("Save as Pending")):u("#save-post").show().val(x("Save Draft"))),!0)},u("#visibility .edit-visibility").on("click",function(t){t.preventDefault(),w.is(":hidden")&&(a(),w.slideDown("fast",function(){w.find('input[type="radio"]').first().trigger("focus")}),u(this).hide())}),w.find(".cancel-post-visibility").on("click",function(t){w.slideUp("fast"),u("#visibility-radio-"+u("#hidden-post-visibility").val()).prop("checked",!0),u("#post_password").val(u("#hidden-post-password").val()),u("#sticky").prop("checked",u("#hidden-post-sticky").prop("checked")),u("#post-visibility-display").html(e),u("#visibility .edit-visibility").show().trigger("focus"),n(),t.preventDefault()}),w.find(".save-post-visibility").on("click",function(t){var e="",i=w.find("input:radio:checked").val();switch(w.slideUp("fast"),u("#visibility .edit-visibility").show().trigger("focus"),n(),"public"!==i&&u("#sticky").prop("checked",!1),i){case"public":e=u("#sticky").prop("checked")?x("Public, Sticky"):x("Public");break;case"private":e=x("Private");break;case"password":e=x("Password Protected")}u("#post-visibility-display").text(e),t.preventDefault()}),w.find("input:radio").on("change",function(){a()}),b.siblings("a.edit-timestamp").on("click",function(t){b.is(":hidden")&&(b.slideDown("fast",function(){u("input, select",b.find(".timestamp-wrap")).first().trigger("focus")}),u(this).hide()),t.preventDefault()}),b.find(".cancel-timestamp").on("click",function(t){b.slideUp("fast").siblings("a.edit-timestamp").show().trigger("focus"),u("#mm").val(u("#hidden_mm").val()),u("#jj").val(u("#hidden_jj").val()),u("#aa").val(u("#hidden_aa").val()),u("#hh").val(u("#hidden_hh").val()),u("#mn").val(u("#hidden_mn").val()),n(),t.preventDefault()}),b.find(".save-timestamp").on("click",function(t){n()&&(b.slideUp("fast"),b.siblings("a.edit-timestamp").show().trigger("focus")),t.preventDefault()}),u("#post").on("submit",function(t){n()||(t.preventDefault(),b.show(),wp.autosave&&wp.autosave.enableButtons(),u("#publishing-action .spinner").removeClass("is-active"))}),k.siblings("a.edit-post-status").on("click",function(t){k.is(":hidden")&&(k.slideDown("fast",function(){k.find("select").trigger("focus")}),u(this).hide()),t.preventDefault()}),k.find(".save-post-status").on("click",function(t){k.slideUp("fast").siblings("a.edit-post-status").show().trigger("focus"),n(),t.preventDefault()}),k.find(".cancel-post-status").on("click",function(t){k.slideUp("fast").siblings("a.edit-post-status").show().trigger("focus"),u("#post_status").val(u("#hidden_post_status").val()),n(),t.preventDefault()})),u("#titlediv").on("click",".edit-slug",function(){!function(){var t,e,i,a=0,n=u("#post_name"),s=n.val(),o=u("#sample-permalink"),l=o.html(),r=u("#sample-permalink a").html(),c=u("#edit-slug-buttons"),d=c.html(),p=u("#editable-post-name-full");for(p.find("img").replaceWith(function(){return this.alt}),p=p.html(),o.html(r),e=u("#editable-post-name"),i=e.html(),c.html('<button type="button" class="save button button-small">'+x("OK")+'</button> <button type="button" class="cancel button-link">'+x("Cancel")+"</button>"),c.children(".save").on("click",function(){var i=e.children("input").val();i!=u("#editable-post-name-full").text()?u.post(ajaxurl,{action:"sample-permalink",post_id:v,new_slug:i,new_title:u("#title").val(),samplepermalinknonce:u("#samplepermalinknonce").val()},function(t){var e=u("#edit-slug-box");e.html(t),e.hasClass("hidden")&&e.fadeIn("fast",function(){e.removeClass("hidden")}),c.html(d),o.html(l),n.val(i),u(".edit-slug").trigger("focus"),wp.a11y.speak(x("Permalink saved"))}):c.children(".cancel").trigger("click")}),c.children(".cancel").on("click",function(){u("#view-post-btn").show(),e.html(i),c.html(d),o.html(l),n.val(s),u(".edit-slug").trigger("focus")}),t=0;t<p.length;++t)"%"==p.charAt(t)&&a++;r=a>p.length/4?"":p,e.html('<input type="text" id="new-post-slug" value="'+r+'" autocomplete="off" />').children("input").on("keydown",function(t){var e=t.which;13===e&&(t.preventDefault(),c.children(".save").trigger("click")),27===e&&c.children(".cancel").trigger("click")}).on("keyup",function(){n.val(this.value)}).trigger("focus")}()}),window.wptitlehint=function(t){var e=u("#"+(t=t||"title")),i=u("#"+t+"-prompt-text");""===e.val()&&i.removeClass("screen-reader-text"),e.on("input",function(){""!==this.value?i.addClass("screen-reader-text"):i.removeClass("screen-reader-text")})},wptitlehint(),t=u("#post-status-info"),c=u("#postdivrich"),!h.length||"ontouchstart"in window?u("#content-resize-handle").hide():t.on("mousedown.wp-editor-resize",function(t){(o="undefined"!=typeof tinymce?tinymce.get("content"):o)&&!o.isHidden()?(r=!0,l=u("#content_ifr").height()-t.pageY):(r=!1,l=h.height()-t.pageY,h.trigger("blur")),f.on("mousemove.wp-editor-resize",D).on("mouseup.wp-editor-resize mouseleave.wp-editor-resize",j),t.preventDefault()}).on("mouseup.wp-editor-resize",j),"undefined"!=typeof tinymce&&(u("#post-formats-select input.post-format").on("change.set-editor-class",function(){var t,e,i=this.id;i&&u(this).prop("checked")&&(t=tinymce.get("content"))&&((e=t.getBody()).className=e.className.replace(/\bpost-format-[^ ]+/,""),t.dom.addClass(e,"post-format-0"==i?"post-format-standard":i),u(document).trigger("editor-classchange"))}),u("#page_template").on("change.set-editor-class",function(){var t,e,i=u(this).val()||"";(i=i.substr(i.lastIndexOf("/")+1,i.length).replace(/\.php$/,"").replace(/\./g,"-"))&&(t=tinymce.get("content"))&&((e=t.getBody()).className=e.className.replace(/\bpage-template-[^ ]+/,""),t.dom.addClass(e,"page-template-"+i),u(document).trigger("editor-classchange"))})),h.on("keydown.wp-autosave",function(t){83===t.which&&(t.shiftKey||t.altKey||_&&(!t.metaKey||t.ctrlKey)||!_&&!t.ctrlKey||(wp.autosave&&wp.autosave.server.triggerSave(),t.preventDefault()))}),"auto-draft"===u("#original_post_status").val()&&window.history.replaceState&&u("#publish").on("click",function(){d=window.location.href,d+=-1!==d.indexOf("?")?"&":"?",d+="wp-post-new-reload=true",window.history.replaceState(null,null,d)}),y.on("success",function(t){var e=u(t.trigger),i=u(".success",e.closest(".copy-to-clipboard-container"));t.clearSelection(),e.trigger("focus"),clearTimeout(s),i.removeClass("hidden"),s=setTimeout(function(){i.addClass("hidden")},3e3),wp.a11y.speak(x("The file URL has been copied to your clipboard"))})}),function(t,o){t(function(){var i,e=t("#content"),a=t("#wp-word-count").find(".word-count"),n=0;function s(){var t=!i||i.isHidden()?e.val():i.getContent({format:"raw"}),t=o.count(t);t!==n&&a.text(t),n=t}t(document).on("tinymce-editor-init",function(t,e){"content"===e.id&&(i=e).on("nodechange keyup",_.debounce(s,1e3))}),e.on("input keyup",_.debounce(s,1e3)),s()})}(jQuery,new wp.utils.WordCounter);