/*! This file is auto-generated */
#wpbody-content #dashboard-widgets.columns-1 .postbox-container {
	width: 100%;
}

#wpbody-content #dashboard-widgets.columns-2 .postbox-container {
	width: 49.5%;
}

#wpbody-content #dashboard-widgets.columns-2 #postbox-container-2,
#wpbody-content #dashboard-widgets.columns-2 #postbox-container-3,
#wpbody-content #dashboard-widgets.columns-2 #postbox-container-4 {
	float: left;
	width: 50.5%;
}

#wpbody-content #dashboard-widgets.columns-3 .postbox-container {
	width: 33.5%;
}

#wpbody-content #dashboard-widgets.columns-3 #postbox-container-1 {
	width: 33%;
}

#wpbody-content #dashboard-widgets.columns-3 #postbox-container-3,
#wpbody-content #dashboard-widgets.columns-3 #postbox-container-4 {
	float: left;
}

#wpbody-content #dashboard-widgets.columns-4 .postbox-container {
	width: 25%;
}

#dashboard-widgets .postbox-container {
	width: 25%;
}

#dashboard-widgets-wrap .columns-3 #postbox-container-4 .empty-container {
	border: none !important;
}

#dashboard-widgets-wrap {
	overflow: hidden;
	margin: 0 -8px;
}

#dashboard-widgets .postbox .inside {
	margin-bottom: 0;
}

#dashboard-widgets .meta-box-sortables {
	display: flow-root; /* avoid margin collapsing between parent and first/last child elements */
	/* Required min-height to make the jQuery UI Sortable drop zone work. */
	min-height: 100px;
	margin: 0 8px 20px;
}

#dashboard-widgets .postbox-container .empty-container {
	outline: 3px dashed #c3c4c7;
	height: 250px;
}

/* Only highlight drop zones when dragging and only in the 2 columns layout. */
.is-dragging-metaboxes #dashboard-widgets .meta-box-sortables {
	outline: 3px dashed #646970;
	/* Prevent margin on the child from collapsing with margin on the parent. */
	display: flow-root;
}

#dashboard-widgets .postbox-container .empty-container:after {
	content: attr(data-emptystring);
	margin: auto;
	position: absolute;
	top: 50%;
	right: 0;
	left: 0;
	transform: translateY( -50% );
	padding: 0 2em;
	text-align: center;
	color: #646970;
	font-size: 16px;
	line-height: 1.5;
	display: none;
}


/* @todo: this was originally in this section, but likely belongs elsewhere */
#the-comment-list td.comment p.comment-author {
	margin-top: 0;
	margin-right: 0;
}

#the-comment-list p.comment-author img {
	float: right;
	margin-left: 8px;
}

#the-comment-list p.comment-author strong a {
	border: none;
}

#the-comment-list td {
	vertical-align: top;
}

#the-comment-list td.comment {
	word-wrap: break-word;
}

#the-comment-list td.comment img {
	max-width: 100%;
}

/* Welcome Panel */
.welcome-panel {
	position: relative;
	overflow: auto;
	margin: 16px 0;
	padding: 23px 10px 0;
	border: 1px solid #c3c4c7;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	background: #fff;
	font-size: 13px;
	line-height: 1.7;
}

.welcome-panel h2 {
	margin: 0;
	font-size: 21px;
	font-weight: 400;
	line-height: 1.2;
}

.welcome-panel h3 {
	margin: 1.33em 0 0;
	font-size: 16px;
}

.welcome-panel li {
	font-size: 14px;
}

.welcome-panel p {
	color: #646970;
}

.welcome-panel li a {
	text-decoration: none;
}

.welcome-panel .about-description {
	font-size: 16px;
	margin: 0;
}

.welcome-panel .welcome-panel-close {
	position: absolute;
	top: 10px;
	left: 10px;
	padding: 10px 21px 10px 15px;
	font-size: 13px;
	line-height: 1.23076923; /* Chrome rounding, needs to be 16px equivalent */
	text-decoration: none;
}

.welcome-panel .welcome-panel-close:before {
	position: absolute;
	top: 8px;
	right: 0;
	transition: all .1s ease-in-out;
}

.wp-core-ui .welcome-panel .button.button-hero {
	margin: 15px 0 3px 13px;
	padding: 12px 36px;
	height: auto;
	line-height: 1.4285714;
	white-space: normal;
}

.welcome-panel-content {
	margin-right: 13px;
	max-width: 1500px;
}

.welcome-panel .welcome-panel-column-container {
	clear: both;
	position: relative;
}

.welcome-panel .welcome-panel-column {
	width: 32%;
	min-width: 200px;
	float: right;
}

.welcome-panel .welcome-panel-column:first-child {
	width: 36%;
}

.welcome-panel-column p.hide-if-no-customize {
	margin-top: 10px;
}

.welcome-panel-column p {
	margin-top: 7px;
	color: #3c434a;
}

.welcome-panel .welcome-widgets-menus {
	line-height: 1.14285714;
}

.welcome-panel .welcome-panel-column ul {
	margin: 0.8em 0 1em 1em;
}

.welcome-panel .welcome-panel-column li {
	line-height: 1.14285714;
	list-style-type: none;
	padding: 0 0 8px;
}

.welcome-panel .welcome-icon {
	background: transparent !important;
}

/* Welcome Panel and Right Now common Icons style */

.welcome-panel .welcome-icon:before,
#dashboard_right_now li a:before,
#dashboard_right_now li span:before,
#dashboard_right_now .search-engines-info:before {
	color: #646970;
	font: normal 20px/1 dashicons;
	speak: never;
	display: inline-block;
	padding: 0 0 0 10px;
	position: relative;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-decoration: none !important;
	vertical-align: top;
}

/* Welcome Panel specific Icons styles */

.welcome-panel .welcome-write-blog:before,
.welcome-panel .welcome-edit-page:before {
	content: "\f119";
	top: -3px;
}

.welcome-panel .welcome-add-page:before {
	content: "\f132";
	top: -1px;
}

.welcome-panel .welcome-setup-home:before {
	content: "\f102";
	top: -1px;
}

.welcome-panel .welcome-view-site:before {
	content: "\f115";
	top: -2px;
}

.welcome-panel .welcome-widgets-menus:before {
	content: "\f116";
	top: -2px;
}

.welcome-panel .welcome-widgets:before {
	content: "\f538";
	top: -2px;
}

.welcome-panel .welcome-menus:before {
	content: "\f163";
	top: -2px;
}

.welcome-panel .welcome-comments:before {
	content: "\f117";
	top: -1px;
}

.welcome-panel .welcome-learn-more:before {
	content: "\f118";
	top: -1px;
}

/* Right Now specific Icons styles */

#dashboard_right_now .search-engines-info:before,
#dashboard_right_now li a:before,
#dashboard_right_now li > span:before { /* get only the first level span to exclude screen-reader-text in mu-storage */
	content: "\f159"; /* generic icon for items added by CPTs ? */
	padding: 0 0 0 5px;
}

#dashboard_right_now .page-count a:before,
#dashboard_right_now .page-count span:before {
	content: "\f105";
}

#dashboard_right_now .post-count a:before,
#dashboard_right_now .post-count span:before {
	content: "\f109";
}

#dashboard_right_now .comment-count a:before {
	content: "\f101";
}

#dashboard_right_now .comment-mod-count a:before {
	content: "\f125";
}

#dashboard_right_now .storage-count a:before {
	content: "\f104";
}

#dashboard_right_now .storage-count.warning a:before {
	content: "\f153";
}

#dashboard_right_now .search-engines-info:before {
	content: "\f348";
}

/* Dashboard WordPress events */

.community-events-errors {
	margin: 0;
}

.community-events-loading {
	padding: 10px 12px 8px;
}

.community-events {
	margin-bottom: 6px;
	padding: 0 12px;
}

.community-events .spinner {
	float: none;
	margin: 5px 2px 0;
	vertical-align: top;
}

.community-events-errors[aria-hidden="true"],
.community-events-errors [aria-hidden="true"],
.community-events-loading[aria-hidden="true"],
.community-events[aria-hidden="true"],
.community-events [aria-hidden="true"] {
	display: none;
}

.community-events .activity-block:first-child,
.community-events h2 {
	padding-top: 12px;
	padding-bottom: 10px;
}

.community-events-form {
	margin: 15px 0 5px;
}

.community-events-form .regular-text {
	width: 40%;
	height: 29px;
	margin: 0;
	vertical-align: top;
}

.community-events li.event-none {
	border-right: 4px solid #72aee6;
}

#dashboard-widgets .community-events li.event-none a {
	text-decoration: underline;
}

.community-events-form label {
	display: inline-block;
	vertical-align: top;
	line-height: 2.15384615;
	height: 28px;
}

.community-events .activity-block > p {
	margin-bottom: 0;
	display: inline;
}

.community-events-toggle-location {
	vertical-align: middle;
}

#community-events-submit {
	margin-right: 3px;
	margin-left: 3px;
}

/* Needs higher specificity than #dashboard-widgets .button-link */
#dashboard-widgets .community-events-cancel.button-link {
	vertical-align: top;
	/* Same properties as the submit button for cross-browsers alignment. */
	line-height: 2;
	height: 28px;
	text-decoration: underline;
}

.community-events ul {
	background-color: #f6f7f7;
	padding-right: 0;
	padding-left: 0;
	padding-bottom: 0;
}

.community-events li {
	margin: 0;
	padding: 8px 12px;
	color: #2c3338;
}
.community-events li:first-child {
	border-top: 1px solid #f0f0f1;
}

.community-events li ~ li {
	border-top: 1px solid #f0f0f1;
}

.community-events .activity-block.last {
	border-bottom: 1px solid #f0f0f1;
	padding-top: 0;
	margin-top: -1px;
}

.community-events .event-info {
	display: block;
}

.event-icon {
	height: 18px;
	padding-left: 10px;
	width: 18px;
	display: none; /* Hide on smaller screens */
}

.event-icon:before {
	color: #646970;
	font-size: 18px;
}
.event-meetup .event-icon:before {
	content: "\f484";
}
.event-wordcamp .event-icon:before {
	content: "\f486";
}

.community-events .event-title {
	font-weight: 600;
	display: block;
}

.community-events .event-date,
.community-events .event-time {
	display: block;
}

.community-events-footer {
	margin-top: 0;
	margin-bottom: 0;
	padding: 12px;
	border-top: 1px solid #f0f0f1;
	color: #dcdcde;
}

/* Safari 10 + VoiceOver specific: without this, the hidden text gets read out before the link. */
.community-events-footer .screen-reader-text {
	height: inherit;
	white-space: nowrap;
}

/* Dashboard WordPress news */

#dashboard_primary .inside {
	margin: 0;
	padding: 0;
}

#dashboard_primary .widget-loading {
	padding: 12px 12px 0;
	margin-bottom: 1em !important; /* Needs to override `.postbox .inside > p:last-child` in common.css */
}

/* Notice when JS is off. */
#dashboard_primary .inside .notice {
	margin: 0;
}

body #dashboard-widgets .postbox form .submit {
	margin: 0;
}

/* Used only for configurable widgets. */
.dashboard-widget-control-form p {
	margin-top: 0;
}

.rssSummary {
	color: #646970;
	margin-top: 4px;
}

#dashboard_primary .rss-widget {
	font-size: 13px;
	padding: 0 12px 0;
}

#dashboard_primary .rss-widget:last-child {
	border-bottom: none;
	padding-bottom: 8px;
}

#dashboard_primary .rss-widget a {
	font-weight: 400;
}

#dashboard_primary .rss-widget span,
#dashboard_primary .rss-widget span.rss-date {
	color: #646970;
}

#dashboard_primary .rss-widget span.rss-date {
	margin-right: 12px;
}

#dashboard_primary .rss-widget ul li {
	padding: 4px 0;
	margin: 0;
}

/* Dashboard right now */

#dashboard_right_now ul {
	margin: 0;
	/* contain floats but don't use overflow: hidden */
	display: inline-block;
	width: 100%;
}

#dashboard_right_now li {
	width: 50%;
	float: right;
	margin-bottom: 10px;
}

#dashboard_right_now .inside {
	padding: 0;
}

#dashboard_right_now .main {
	padding: 0 12px 11px;
}

#dashboard_right_now .main p {
	margin: 0;
}

#dashboard_right_now #wp-version-message .button {
	float: left;
	position: relative;
	top: -5px;
	margin-right: 5px;
}

#dashboard_right_now p.search-engines-info {
	margin: 1em 0;
}

.mu-storage {
	overflow: hidden;
}

#dashboard-widgets h3.mu-storage {
	margin: 0 0 10px;
	padding: 0;
	font-size: 14px;
	font-weight: 400;
}

/* Dashboard right now - Colors */

#dashboard_right_now .sub {
	color: #50575e;
	background: #f6f7f7;
	border-top: 1px solid #f0f0f1;
	padding: 10px 12px 6px 12px;
}

#dashboard_right_now .sub h3 {
	color: #50575e;
}

#dashboard_right_now .sub p {
	margin: 0 0 1em;
}

#dashboard_right_now .warning a:before,
#dashboard_right_now .warning span:before {
	color: #d63638;
}

/* Dashboard Quick Draft */

#dashboard_quick_press .inside {
	margin: 0;
	padding: 0;
}

#dashboard_quick_press div.updated {
	margin-bottom: 10px;
	border: 1px solid #f0f0f1;
	border-width: 1px 0 1px 1px;
}

#dashboard_quick_press form {
	margin: 12px;
}

#dashboard_quick_press .drafts {
	padding: 10px 0 0;
}

/* Dashboard Quick Draft - Form styling */

#dashboard_quick_press label {
	display: inline-block;
	margin-bottom: 4px;
}

#dashboard_quick_press input,
#dashboard_quick_press textarea {
	box-sizing: border-box;
	margin: 0;
}

#dashboard-widgets .postbox form .submit {
	margin: -39px 0;
	float: left;
}

#description-wrap {
	margin-top: 12px;
}

#quick-press textarea#content {
	min-height: 90px;
	max-height: 1300px;
	margin: 0 0 8px;
	padding: 6px 7px;
	resize: none;
}

/* Dashboard Quick Draft - Drafts list */

.js #dashboard_quick_press .drafts {
	border-top: 1px solid #f0f0f1;
}

#dashboard_quick_press .drafts abbr {
	border: none;
}

#dashboard_quick_press .drafts .view-all {
	float: left;
	margin: 0 0 0 12px;
}

#dashboard_primary a.rsswidget {
	font-weight: 400;
}

#dashboard_quick_press .drafts ul {
	margin: 0 12px;
}

#dashboard_quick_press .drafts li {
	margin-bottom: 1em;
}
#dashboard_quick_press .drafts li time {
	color: #646970;
}

#dashboard_quick_press .drafts p {
	margin: 0;
	word-wrap: break-word;
}

#dashboard_quick_press .draft-title {
	word-wrap: break-word;
}

#dashboard_quick_press .draft-title a,
#dashboard_quick_press .draft-title time {
	margin: 0 0 0 5px;
}

/* Dashboard common styles */

#dashboard-widgets h4, /* Back-compat for pre-4.4 */
#dashboard-widgets h3,
#dashboard_quick_press .drafts h2 {
	margin: 0 12px 8px;
	padding: 0;
	font-size: 14px;
	font-weight: 400;
	color: #1d2327;
}

#dashboard_quick_press .drafts h2 {
	line-height: inherit;
}

#dashboard-widgets .inside h4, /* Back-compat for pre-4.4 */
#dashboard-widgets .inside h3 {
	margin-right: 0;
	margin-left: 0;
}

/* Dashboard activity widget */

#dashboard_activity .comment-meta span.approve:before {
	content: "\f227";
	font: 20px/.5 dashicons;
	margin-right: 5px;
	vertical-align: middle;
	position: relative;
	top: -1px;
	margin-left: 2px;
}

#dashboard_activity .inside {
	margin: 0;
	padding-bottom: 0;
}

#dashboard_activity .no-activity {
	overflow: hidden;
	padding: 12px 0;
	text-align: center;
}

#dashboard_activity .no-activity p {
	color: #646970;
	font-size: 16px;
}

#dashboard_activity .subsubsub {
	float: none;
	border-top: 1px solid #f0f0f1;
	margin: 0 -12px;
	padding: 8px 12px 4px;
}

#dashboard_activity .subsubsub a .count,
#dashboard_activity .subsubsub a.current .count {
	color: #646970; /* white background on the dashboard but #f0f0f1 on list tables */
}

#future-posts ul,
#published-posts ul {
	clear: both;
	margin-bottom: 0;
}

#future-posts li,
#published-posts li {
	margin-bottom: 8px;
}

#future-posts ul span,
#published-posts ul span {
	display: inline-block;
	margin-left: 5px;
	min-width: 150px;
	color: #646970;
}

.activity-block {
	border-bottom: 1px solid #f0f0f1;
	margin: 0 -12px;
	padding: 8px 12px 4px;
}

.activity-block:last-child {
	border-bottom: none;
}

.activity-block .subsubsub li {
	color: #dcdcde;
}

/* Dashboard activity widget - Comments */
/* @todo: needs serious de-duplication */

#activity-widget #the-comment-list tr.undo,
#activity-widget #the-comment-list div.undo {
	background: none;
	padding: 6px 0;
	margin-right: 12px;
}

#activity-widget #the-comment-list .comment-item {
	background: #f6f7f7;
	padding: 12px;
	position: relative;
}

#activity-widget #the-comment-list .avatar {
	position: absolute;
	top: 12px;
}

#activity-widget #the-comment-list .dashboard-comment-wrap.has-avatar {
	padding-right: 63px;
}

#activity-widget #the-comment-list .dashboard-comment-wrap blockquote {
	margin: 1em 0;
}

#activity-widget #the-comment-list .comment-item p.row-actions {
	margin: 4px 0 0 0;
}

#activity-widget #the-comment-list .comment-item:first-child {
	border-top: 1px solid #f0f0f1;
}

#activity-widget #the-comment-list .unapproved {
	background-color: #fcf9e8;
}

#activity-widget #the-comment-list .unapproved:before {
	content: "";
	display: block;
	position: absolute;
	right: 0;
	top: 0;
	bottom: 0;
	background: #d63638;
	width: 4px;
}

#activity-widget #the-comment-list .spam-undo-inside .avatar,
#activity-widget #the-comment-list .trash-undo-inside .avatar {
	position: relative;
	top: 0;
}

/* Browse happy box */

#dashboard-widgets #dashboard_browser_nag.postbox .inside {
	margin: 10px;
}

.postbox .button-link .edit-box {
	display: none;
}

.edit-box {
	opacity: 0;
}

.hndle:hover .edit-box,
.edit-box:focus {
	opacity: 1;
}

#dashboard-widgets form .input-text-wrap input {
	width: 100%;
}

#dashboard-widgets form .textarea-wrap textarea {
	width: 100%;
}

#dashboard-widgets .postbox form .submit {
	float: none;
	margin: .5em 0 0;
	padding: 0;
	border: none;
}

#dashboard-widgets-wrap #dashboard-widgets .postbox form .submit #publish {
	min-width: 0;
}

#dashboard-widgets li a,
#dashboard-widgets .button-link,
.community-events-footer a {
	text-decoration: none;
}

#dashboard-widgets h2 a {
	text-decoration: underline;
}

#dashboard-widgets .hndle .postbox-title-action {
	float: left;
	line-height: 1.2;
}

#dashboard_plugins h5 {
	font-size: 14px;
}

/* Recent Comments */

#latest-comments #the-comment-list {
	position: relative;
	margin: 0 -12px;
}

#activity-widget #the-comment-list .comment,
#activity-widget #the-comment-list .pingback {
	box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.06);
}

#activity-widget .comments #the-comment-list .alt {
	background-color: transparent;
}

#activity-widget #latest-comments #the-comment-list .comment-item {
	/* the row-actions paragraph is output only for users with 'edit_comment' capabilities,
	   for other users this needs a min height equal to the gravatar image */
	min-height: 50px;
	margin: 0;
	padding: 12px;
}

#latest-comments #the-comment-list .pingback {
	padding-right: 12px !important;
}

#latest-comments #the-comment-list .comment-item:first-child {
	border-top: none;
}

#latest-comments #the-comment-list .comment-meta {
	line-height: 1.5;
	margin: 0;
	color: #646970;
}

#latest-comments #the-comment-list .comment-meta cite {
	font-style: normal;
	font-weight: 400;
}

#latest-comments #the-comment-list .comment-item blockquote,
#latest-comments #the-comment-list .comment-item blockquote p {
	margin: 0;
	padding: 0;
	display: inline;
}

#latest-comments #the-comment-list .comment-item p.row-actions {
	margin: 3px 0 0;
	padding: 0;
	font-size: 13px;
}

/* Feeds */
.rss-widget ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

a.rsswidget {
	font-size: 13px;
	font-weight: 600;
	line-height: 1.4;
}

.rss-widget ul li {
	line-height: 1.5;
	margin-bottom: 12px;
}

.rss-widget span.rss-date {
	color: #646970;
	font-size: 13px;
	margin-right: 3px;
}

.rss-widget cite {
	display: block;
	text-align: left;
	margin: 0 0 1em;
	padding: 0;
}

.rss-widget cite:before {
	content: "\2014";
}

.dashboard-comment-wrap {
	word-wrap: break-word;
}

/* Browser Nag */
#dashboard_browser_nag a.update-browser-link {
	font-size: 1.2em;
	font-weight: 600;
}

#dashboard_browser_nag a {
	text-decoration: underline;
}

#dashboard_browser_nag p.browser-update-nag.has-browser-icon {
	padding-left: 125px;
}

#dashboard_browser_nag .browser-icon {
	margin-top: -35px;
}

#dashboard_browser_nag.postbox.browser-insecure {
	background-color: #b32d2e;
	border-color: #b32d2e;
}

#dashboard_browser_nag.postbox {
	background-color: #dba617;
	background-image: none;
	border-color: #f0c33c;
	color: #fff;
	box-shadow: none;
}

#dashboard_browser_nag.postbox.browser-insecure h2 {
	border-bottom-color: #e65054;
	color: #fff;
}

#dashboard_browser_nag.postbox h2 {
	border-bottom-color: #f5e6ab;
	background: transparent none;
	color: #fff;
	box-shadow: none;
}

#dashboard_browser_nag a {
	color: #fff;
}

#dashboard_browser_nag h2.hndle {
	border: none;
	font-weight: 600;
	font-size: 20px;
	padding-top: 10px;
}

.postbox#dashboard_browser_nag p a.dismiss {
	font-size: 14px;
}

.postbox#dashboard_browser_nag p,
.postbox#dashboard_browser_nag a,
.postbox#dashboard_browser_nag p.browser-update-nag {
	font-size: 16px;
}

/* PHP Nag */
#dashboard_php_nag .dashicons-warning {
	color: #dba617;
	padding-left: 6px;
}

#dashboard_php_nag.php-insecure .dashicons-warning {
	color: #d63638;
}

#dashboard_php_nag h2 {
	display: inline-block;
}

#dashboard_php_nag p {
	margin: 12px 0;
}

#dashboard_php_nag h3 {
	font-weight: 600;
}

#dashboard_php_nag .button .dashicons-external {
	line-height: 25px;
}

/* =Media Queries
-------------------------------------------------------------- */

/* one column on the dash */
@media only screen and (max-width: 799px) {
	#wpbody-content #dashboard-widgets .postbox-container {
		width: 100%;
	}

	#dashboard-widgets .meta-box-sortables {
		min-height: 0;
	}

	.is-dragging-metaboxes #dashboard-widgets .meta-box-sortables {
		min-height: 100px;
	}

	#dashboard-widgets .meta-box-sortables.empty-container {
		margin-bottom: 0;
	}
}

/* two columns on the dash, but keep the setting if one is selected */
@media only screen and (min-width: 800px) and (max-width: 1499px) {
	#wpbody-content #dashboard-widgets .postbox-container {
		width: 49.5%;
	}

	#wpbody-content #dashboard-widgets #postbox-container-2,
	#wpbody-content #dashboard-widgets #postbox-container-3,
	#wpbody-content #dashboard-widgets #postbox-container-4 {
		float: left;
		width: 50.5%;
	}

	#dashboard-widgets #postbox-container-3 .empty-container,
	#dashboard-widgets #postbox-container-4 .empty-container {
		outline: none;
		height: 0;
		min-height: 0;
		margin-bottom: 0;
	}

	#dashboard-widgets #postbox-container-3 .empty-container:after,
	#dashboard-widgets #postbox-container-4 .empty-container:after {
		display: none;
	}

	#wpbody #wpbody-content #dashboard-widgets.columns-1 .postbox-container {
		width: 100%;
	}

	#wpbody #dashboard-widgets .metabox-holder.columns-1 .postbox-container .empty-container {
		outline: none;
		height: 0;
		min-height: 0;
		margin-bottom: 0;
	}

	/* show the radio buttons for column prefs only for one or two columns */
	.index-php .screen-layout,
	.index-php .columns-prefs {
		display: block;
	}

	.columns-prefs .columns-prefs-3,
	.columns-prefs .columns-prefs-4 {
		display: none;
	}

	#dashboard-widgets .postbox-container .empty-container:after {
		display: block;
	}
}

/* three columns on the dash */
@media only screen and (min-width: 1500px) and (max-width: 1800px) {
	#wpbody-content #dashboard-widgets .postbox-container {
		width: 33.5%;
	}

	#wpbody-content #dashboard-widgets #postbox-container-1 {
		width: 33%;
	}

	#wpbody-content #dashboard-widgets #postbox-container-3,
	#wpbody-content #dashboard-widgets #postbox-container-4 {
		float: left;
	}

	#dashboard-widgets #postbox-container-4 .empty-container {
		outline: none;
		height: 0;
		min-height: 0;
		margin-bottom: 0;
	}

	#dashboard-widgets #postbox-container-4 .empty-container:after {
		display: none;
	}

	#dashboard-widgets .postbox-container .empty-container:after {
		display: block;
	}
}

/* Always show the "Drag boxes here" CSS generated content on large screens. */
@media only screen and (min-width: 1801px) {
	#dashboard-widgets .postbox-container .empty-container:after {
		display: block;
	}
}

@media screen and (max-width: 870px) {
	.welcome-panel .welcome-panel-column,
	.welcome-panel .welcome-panel-column:first-child {
		display: block;
		float: none;
		width: 100%;
	}

	.welcome-panel .welcome-panel-column li {
		display: inline-block;
		margin-left: 13px;
	}

	.welcome-panel .welcome-panel-column ul {
		margin: 0.4em 0 0;
	}

}

@media screen and (max-width: 782px) {
	#dashboard-widgets h2 {
		padding: 12px;
	}

	#dashboard_recent_comments #the-comment-list .comment-item .avatar {
		height: 30px;
		width: 30px;
		margin: 4px 0 5px 10px;
	}

	.community-events-toggle-location {
		height: 38px;
		vertical-align: baseline;
	}

	.community-events-form .regular-text {
		height: 32px;
	}

	#community-events-submit {
		margin-bottom: 0;
		/* Override .wp-core-ui .button */
		vertical-align: top;
	}

	.community-events-form label,
	#dashboard-widgets .community-events-cancel.button-link {
		/* Same properties as the submit button for cross-browsers alignment. */
		font-size: 14px;
		line-height: normal;
		height: auto;
		padding: 6px 0;
		border: 1px solid transparent;
	}

	.community-events .spinner {
		margin-top: 7px;
	}
}

/* Smartphone */
@media screen and (max-width: 600px) {
	/* Keep the close icon from overlapping the Welcome text. */
	.welcome-panel .welcome-panel-close {
		overflow: hidden;
		text-indent: 40px;
		white-space: nowrap;
		width: 20px;
		height: 20px;
		padding: 5px;
		top: 5px;
		left: 5px;
	}

	/* Make the close icon larger for tappability. */
	.welcome-panel .welcome-panel-close:before {
		font-size: 20px;
		top: 5px;
		right: -35px;
	}
}

@media screen and (min-width: 355px) {
	.community-events .event-info {
		display: table-row;
		float: right;
		max-width: 59%;
	}

	.event-icon,
	.event-icon[aria-hidden="true"] {
		display: table-cell;
	}

	.event-info-inner {
		display: table-cell;
	}

	.community-events .event-date-time {
		float: left;
		max-width: 39%;
	}

	.community-events .event-date,
	.community-events .event-time {
		text-align: left;
	}
}
