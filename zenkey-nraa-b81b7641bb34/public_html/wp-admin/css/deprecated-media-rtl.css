/*! This file is auto-generated */
/* Styles for the media library iframe (not used on the Library screen) */

div#media-upload-header {
	margin: 0;
	padding: 5px 5px 0;
	font-weight: 600;
	position: relative;
	border-bottom: 1px solid #dcdcde;
	background: #f6f7f7;
}

#sidemenu {
	overflow: hidden;
	float: none;
	position: relative;
	right: 0;
	bottom: -1px;
	margin: 0 5px;
	padding-right: 10px;
	list-style: none;
	font-size: 12px;
	font-weight: 400;
}

#sidemenu a {
	padding: 0 7px;
	display: block;
	float: right;
	line-height: 28px;
	border-top: 1px solid #f6f7f7;
	border-bottom: 1px solid #dcdcde;
	background-color: #f6f7f7;
	text-decoration: none;
	transition: none;
}

#sidemenu li {
	display: inline;
	line-height: 200%;
	list-style: none;
	text-align: center;
	white-space: nowrap;
	margin: 0;
	padding: 0;
}

#sidemenu a.current {
	font-weight: 400;
	padding-right: 6px;
	padding-left: 6px;
	border: 1px solid #dcdcde;
	border-bottom-color: #f0f0f1;
	background-color: #f0f0f1;
	color: #000;
}

#media-upload:after { /* clearfix */
	content: "";
	display: table;
	clear: both;
}

#media-upload .slidetoggle {
	border-top-color: #dcdcde;
}

#media-upload input[type="radio"] {
	padding: 0;
}

.media-upload-form label.form-help,
td.help {
	color: #646970;
}

form {
	margin: 1em;
}

#search-filter {
	text-align: left;
}

th {
	position: relative;
}

.media-upload-form label.form-help, td.help {
	font-family: sans-serif;
	font-style: italic;
	font-weight: 400;
}

.media-upload-form p.help {
	margin: 0;
	padding: 0;
}

.media-upload-form fieldset {
	width: 100%;
	border: none;
	text-align: justify;
	margin: 0 0 1em 0;
	padding: 0;
}

/* specific to the image upload form */

.image-align-none-label {
	background: url(../images/align-none.png) no-repeat center right;
}

.image-align-left-label {
	background: url(../images/align-left.png) no-repeat center right;
}

.image-align-center-label {
	background: url(../images/align-center.png) no-repeat center right;
}

.image-align-right-label {
	background: url(../images/align-right.png) no-repeat center right;
}

tr.image-size td {
	width: 460px;
}

tr.image-size div.image-size-item {
	margin: 0 0 5px;
}

#library-form .progress,
#gallery-form .progress,
.insert-gallery,
.describe.startopen,
.describe.startclosed {
	display: none;
}

.media-item .thumbnail {
	max-width: 128px;
	max-height: 128px;
}

thead.media-item-info tr {
	background-color: transparent;
}

.form-table thead.media-item-info {
	border: 8px solid #fff;
}

abbr.required,
span.required {
	text-decoration: none;
	border: none;
}

.describe label {
	display: inline;
}

.describe td.error {
	padding: 2px 8px;
}

.describe td.A1 {
	width: 132px;
}

.describe input[type="text"],
.describe textarea {
	width: 460px;
	border-width: 1px;
	border-style: solid;
}

/* Specific to Uploader */

#media-upload p.ml-submit {
	padding: 1em 0;
}

#media-upload p.help,
#media-upload label.help {
	font-family: sans-serif;
	font-style: italic;
	font-weight: 400;
}

#media-upload .ui-sortable .media-item {
	cursor: move;
}

#media-upload tr.image-size {
	margin-bottom: 1em;
	height: 3em;
}

#media-upload #filter {
	width: 623px;
}

#media-upload #filter .subsubsub {
	margin: 8px 0;
}

#media-upload .tablenav-pages a,
#media-upload .tablenav-pages .current {
	display: inline-block;
	padding: 4px 5px 6px;
	font-size: 16px;
	line-height: 1;
	text-align: center;
	text-decoration: none;
}

#media-upload .tablenav-pages a {
	min-width: 17px;
	border: 1px solid #c3c4c7;
	background: #f6f7f7;
}

#filter .tablenav select {
	border-style: solid;
	border-width: 1px;
	padding: 2px;
	vertical-align: top;
	width: auto;
}

#media-upload .del-attachment {
	display: none;
	margin: 5px 0;
}

.menu_order {
	float: left;
	font-size: 11px;
	margin: 8px 10px 0;
}

.menu_order_input {
	border: 1px solid #dcdcde;
	font-size: 10px;
	padding: 1px;
	width: 23px;
}

.ui-sortable-helper {
	background-color: #fff;
	border: 1px solid #a7aaad;
	opacity: 0.6;
	filter: alpha(opacity=60);
}

#media-upload th.order-head {
	width: 20%;
	text-align: center;
}

#media-upload th.actions-head {
	width: 25%;
	text-align: center;
}

#media-upload a.wp-post-thumbnail {
	margin: 0 20px;
}

#media-upload .widefat {
	border-style: solid solid none;
}

.sorthelper {
	height: 37px;
	width: 623px;
	display: block;
}

#gallery-settings th.label {
	width: 160px;
}

#gallery-settings #basic th.label {
	padding: 5px 0 5px 5px;
}

#gallery-settings .title {
	clear: both;
	padding: 0 0 3px;
	font-size: 1.6em;
	border-bottom: 1px solid #dcdcde;
}

h3.media-title {
	font-size: 1.6em;
}

h4.media-sub-title {
	border-bottom: 1px solid #dcdcde;
	font-size: 1.3em;
	margin: 12px;
	padding: 0 0 3px;
}

#gallery-settings .title,
h3.media-title,
h4.media-sub-title {
	font-family: Georgia,"Times New Roman",Times,serif;
	font-weight: 400;
	color: #50575e;
}

#gallery-settings .describe td {
	vertical-align: middle;
	height: 3em;
}

#gallery-settings .describe th.label {
	padding-top: .5em;
	text-align: right;
}

#gallery-settings .describe {
	padding: 5px;
	width: 100%;
	clear: both;
	cursor: default;
	background: #fff;
}

#gallery-settings .describe select {
	width: 15em;
}

#gallery-settings .describe select option,
#gallery-settings .describe td {
	padding: 0;
}

#gallery-settings label,
#gallery-settings legend {
	font-size: 13px;
	color: #3c434a;
	margin-left: 15px;
}

#gallery-settings .align .field label {
	margin: 0 3px 0 1em;
}

#gallery-settings p.ml-submit {
	border-top: 1px solid #dcdcde;
}

#gallery-settings select#columns {
	width: 6em;
}

#sort-buttons {
	font-size: 0.8em;
	margin: 3px 0 -8px 25px;
	text-align: left;
	max-width: 625px;
}

#sort-buttons a {
	text-decoration: none;
}

#sort-buttons #asc,
#sort-buttons #showall {
	padding-right: 5px;
}

#sort-buttons span {
	margin-left: 25px;
}

p.media-types {
	margin: 0;
	padding: 1em;
}

p.media-types-required-info {
	padding-top: 0;
}

tr.not-image {
	display: none;
}

table.not-image tr.not-image {
	display: table-row;
}

table.not-image tr.image-only {
	display: none;
}

/**
 * HiDPI Displays
 */
@media print,
  (-webkit-min-device-pixel-ratio: 1.25),
  (min-resolution: 120dpi) {

	.image-align-none-label {
		background-image: url(../images/align-none-2x.png?ver=20120916);
		background-size: 21px 15px;
	}

	.image-align-left-label {
		background-image: url(../images/align-left-2x.png?ver=20120916);
		background-size: 22px 15px;
	}

	.image-align-center-label {
		background-image: url(../images/align-center-2x.png?ver=20120916);
		background-size: 21px 15px;
	}

	.image-align-right-label {
		background-image: url(../images/align-right-2x.png?ver=20120916);
		background-size: 22px 15px;
	}
}
