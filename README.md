# NRAA OPMP (Online Performance Management Platform)

**National Rifle Association of Australia - Online Performance Management Platform**

A modern web application for managing shooter profiles, performance events, competitions, and results for the National Rifle Association of Australia.

## Project Structure

```
nraa-modern/
├── backend/          # Node.js + Express + Prisma API
│   ├── src/
│   │   ├── server.ts
│   │   ├── routes/
│   │   └── generated/
│   └── package.json
├── frontend/         # Next.js + React + TypeScript
│   ├── src/
│   │   └── app/
│   └── package.json
└── README.md
```

## Features

### Backend (API)
- **Database**: Connected to existing NRAA MySQL database (48 tables, 17,439+ shooters)
- **API Endpoints**: RESTful API for shooters, events, and authentication
- **Technology**: Node.js, Express, Prisma ORM, TypeScript

### Frontend (Web Application)
- **Framework**: Next.js 15 with React and TypeScript
- **Styling**: Tailwind CSS for modern, responsive design
- **State Management**: React Query for API data management
- **Features**: 
  - Shooter profile management
  - Performance event tracking
  - Competition management
  - Results and analytics

## Development

### Local Development
```bash
# Backend
cd nraa-modern/backend
npm install
npm run dev

# Frontend (in another terminal)
cd nraa-modern/frontend
npm install
npm run dev
```

## Docker Deployment

### Prerequisites
- Docker and Docker Compose installed
- Access to local registry at `tower.local:5000`

### Quick Start
```bash
cd nraa-modern

# 1. Configure environment
cp .env.example .env
# Edit .env with your database credentials

# 2. Build and push images to local registry
./build.sh

# 3. Deploy the application
./deploy.sh
```

### Manual Docker Commands
```bash
cd nraa-modern

# Build images
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Docker Images
All images are stored in the local registry at `tower.local:5000`:
- `tower.local:5000/nraa-opmp-backend:latest`
- `tower.local:5000/nraa-opmp-frontend:latest`
- `tower.local:5000/node:18-alpine` (base image)
- `tower.local:5000/mysql:8.0` (database)
- `tower.local:5000/nginx:alpine` (reverse proxy)

### Service URLs
- **Application**: http://localhost (via Nginx)
- **Frontend Direct**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Database**: localhost:3306

### Health Checks
- Frontend: http://localhost:3000/health
- Backend: http://localhost:3001/health
- Nginx: http://localhost/health

## Database

The application connects to the existing NRAA database containing:
- 17,439+ shooter profiles
- Historical competition data
- Event and match records
- Performance results and rankings

## Technology Stack

- **Backend**: Node.js, Express, Prisma, TypeScript
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Database**: MySQL (existing NRAA database)
- **State Management**: React Query
- **Development**: Hot reload, TypeScript compilation

## Environment

- **Development Server**: http://localhost:3001
- **API Server**: http://localhost:3001/api
- **Database**: MySQL connection via Prisma

---

*NRAA OPMP - Modernizing performance management for Australian rifle shooting*
