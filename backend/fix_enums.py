#!/usr/bin/env python3
"""
Fix duplicate enum values in Prisma schema
"""

import re

def fix_enum_values(content):
    """Fix duplicate enum values by making them unique"""
    
    # Fix range_durations_distance enum
    content = re.sub(
        r'enum range_durations_distance \{[^}]+\}',
        '''enum range_durations_distance {
  y300   @map("300y")
  y400   @map("400y")
  y500   @map("500y")
  y600   @map("600y")
  y700   @map("700y")
  y800   @map("800y")
  y900   @map("900y")
  y1000  @map("1000y")
  y1100  @map("1100y")
  y1200  @map("1200y")
  y1500  @map("1500y")
  m300   @map("300m")
  m400   @map("400m")
  m500   @map("500m")
  m600   @map("600m")
  m700   @map("700m")
  m800   @map("800m")
  m900   @map("900m")
  m1000  @map("1000m")
  m1100  @map("1100m")
  m1200  @map("1200m")
  m1300  @map("1300m")
  m1400  @map("1400m")
  m1500  @map("1500m")
  agg
}''',
        content,
        flags=re.DOTALL
    )
    
    # Fix ranges_distance enum
    content = re.sub(
        r'enum ranges_distance \{[^}]+\}',
        '''enum ranges_distance {
  y100   @map("100y")
  y200   @map("200y")
  y300   @map("300y")
  y400   @map("400y")
  y500   @map("500y")
  y600   @map("600y")
  y700   @map("700y")
  y800   @map("800y")
  y900   @map("900y")
  y1000  @map("1000y")
  y1100  @map("1100y")
  y1200  @map("1200y")
  y1500  @map("1500y")
  m100   @map("100m")
  m200   @map("200m")
  m300   @map("300m")
  m400   @map("400m")
  m500   @map("500m")
  m600   @map("600m")
  m700   @map("700m")
  m800   @map("800m")
  m900   @map("900m")
  m1000  @map("1000m")
  m1100  @map("1100m")
  m1200  @map("1200m")
  m1300  @map("1300m")
  m1400  @map("1400m")
  m1500  @map("1500m")
  agg
}''',
        content,
        flags=re.DOTALL
    )
    
    return content

def main():
    # Read the schema file
    with open('prisma/schema.prisma', 'r') as f:
        content = f.read()
    
    # Fix the enum values
    fixed_content = fix_enum_values(content)
    
    # Write back the fixed content
    with open('prisma/schema.prisma', 'w') as f:
        f.write(fixed_content)
    
    print("Fixed enum values in schema.prisma")

if __name__ == '__main__':
    main()
