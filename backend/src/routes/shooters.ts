const { Router } = require('express');
const { z } = require('zod');
const { PrismaClient } = require('../generated/prisma');

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const shooterQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  search: z.string().optional(),
  association: z.string().optional(),
  status: z.enum(['Active', 'Inactive', 'Deceased', 'Honorary']).optional()
});

// GET /api/shooters - List shooters with pagination and search
router.get('/', async (req, res) => {
  try {
    const { page, limit, search, association, status } = shooterQuerySchema.parse(req.query);
    
    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (search) {
      const searchNumber = parseInt(search);
      where.OR = [
        // Search by shooter ID number
        ...(isNaN(searchNumber) ? [] : [{ sid: searchNumber }]),
        // Search by name and email in details
        {
          shooter_details: {
            OR: [
              { first_name: { contains: search } },
              { last_name: { contains: search } },
              { email: { contains: search } }
            ]
          }
        },
        // Search by club
        { club: { contains: search } }
      ];
    }
    
    if (association) {
      where.shooter_membership = {
        association_id: parseInt(association)
      };
    }
    
    // Get shooters with related data
    const [shooters, total] = await Promise.all([
      prisma.shooters.findMany({
        where,
        include: {
          shooter_details: true,
          shooter_membership: {
            include: {
              associations: true
            }
          },
          shooter_addresses: {
            where: { type: 'primary' }
          }
        },
        skip,
        take: limit,
        orderBy: [
          { sid: 'asc' },
          { id: 'asc' }
        ]
      }),
      prisma.shooters.count({ where })
    ]);
    
    res.json({
      success: true,
      data: shooters,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      });
    }
    
    console.error('Error fetching shooters:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch shooters'
    });
  }
});

// GET /api/shooters/:id - Get single shooter with full details
router.get('/:id', async (req, res) => {
  try {
    const shooterId = parseInt(req.params.id);
    
    if (isNaN(shooterId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid shooter ID'
      });
    }
    
    const shooter = await prisma.shooters.findUnique({
      where: { id: shooterId },
      include: {
        shooter_details: true,
        shooter_membership: {
          include: {
            associations: true
          }
        },
        shooter_addresses: true,
        results: {
          include: {
            matches: {
              include: {
                events: true
              }
            },
            grades: true
          },
          orderBy: {
            matches: {
              events: {
                start_date: 'desc'
              }
            }
          },
          take: 20 // Limit recent results
        }
      }
    });
    
    if (!shooter) {
      return res.status(404).json({
        success: false,
        error: 'Shooter not found'
      });
    }
    
    res.json({
      success: true,
      data: shooter
    });
  } catch (error) {
    console.error('Error fetching shooter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch shooter'
    });
  }
});

// GET /api/shooters/stats/summary - Get shooter statistics
router.get('/stats/summary', async (req, res) => {
  try {
    const [
      totalShooters,
      activeShooters,
      associationStats
    ] = await Promise.all([
      prisma.shooters.count(),
      prisma.shooters.count({ where: { status: 'Active' } }),
      prisma.shooters.groupBy({
        by: ['shooter_membership'],
        _count: true,
        where: {
          shooter_membership: {
            isNot: null
          }
        }
      })
    ]);
    
    res.json({
      success: true,
      data: {
        total: totalShooters,
        active: activeShooters,
        inactive: totalShooters - activeShooters,
        associations: associationStats
      }
    });
  } catch (error) {
    console.error('Error fetching shooter stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch shooter statistics'
    });
  }
});

module.exports = router;
