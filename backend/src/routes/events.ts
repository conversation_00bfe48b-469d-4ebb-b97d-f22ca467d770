const { Router } = require('express');
const { z } = require('zod');
const { PrismaClient } = require('../generated/prisma');

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const eventQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  year: z.string().optional(),
  association: z.string().optional(),
  upcoming: z.string().optional().transform(val => val === 'true')
});

// GET /api/events - List events with pagination and filters
router.get('/', async (req, res) => {
  try {
    const { page, limit, year, association, upcoming } = eventQuerySchema.parse(req.query);
    
    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: any = {};
    
    if (association) {
      where.association_id = parseInt(association);
    }
    
    if (year) {
      const yearStart = new Date(`${year}-01-01`).getTime() / 1000;
      const yearEnd = new Date(`${year}-12-31`).getTime() / 1000;
      where.start_date = {
        gte: yearStart,
        lte: yearEnd
      };
    }
    
    if (upcoming) {
      const now = Math.floor(Date.now() / 1000);
      where.start_date = {
        gte: now
      };
    }
    
    // Get events with related data
    const [events, total] = await Promise.all([
      prisma.events.findMany({
        where,
        include: {
          associations: true,
          matches: {
            include: {
              _count: {
                select: {
                  results: true
                }
              }
            }
          },
          _count: {
            select: {
              matches: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          start_date: 'desc'
        }
      }),
      prisma.events.count({ where })
    ]);
    
    // Transform Unix timestamps to readable dates
    const transformedEvents = events.map(event => ({
      ...event,
      start_date_formatted: new Date(event.start_date * 1000).toISOString(),
      end_date_formatted: new Date(event.end_date * 1000).toISOString()
    }));
    
    res.json({
      success: true,
      data: transformedEvents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.errors
      });
    }
    
    console.error('Error fetching events:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch events'
    });
  }
});

// GET /api/events/:id - Get single event with full details
router.get('/:id', async (req, res) => {
  try {
    const eventId = parseInt(req.params.id);
    
    if (isNaN(eventId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid event ID'
      });
    }
    
    const event = await prisma.events.findUnique({
      where: { id: eventId },
      include: {
        associations: true,
        matches: {
          include: {
            results: {
              include: {
                shooters: {
                  include: {
                    shooter_details: true
                  }
                },
                grades: true
              },
              orderBy: {
                place: 'asc'
              }
            },
            _count: {
              select: {
                results: true
              }
            }
          },
          orderBy: {
            number: 'asc'
          }
        }
      }
    });
    
    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Event not found'
      });
    }
    
    // Transform Unix timestamps
    const transformedEvent = {
      ...event,
      start_date_formatted: new Date(event.start_date * 1000).toISOString(),
      end_date_formatted: new Date(event.end_date * 1000).toISOString()
    };
    
    res.json({
      success: true,
      data: transformedEvent
    });
  } catch (error) {
    console.error('Error fetching event:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch event'
    });
  }
});

// GET /api/events/stats/summary - Get event statistics
router.get('/stats/summary', async (req, res) => {
  try {
    const currentYear = new Date().getFullYear();
    const yearStart = new Date(`${currentYear}-01-01`).getTime() / 1000;
    const yearEnd = new Date(`${currentYear}-12-31`).getTime() / 1000;
    
    const [
      totalEvents,
      currentYearEvents,
      upcomingEvents,
      associationStats
    ] = await Promise.all([
      prisma.events.count(),
      prisma.events.count({
        where: {
          start_date: {
            gte: yearStart,
            lte: yearEnd
          }
        }
      }),
      prisma.events.count({
        where: {
          start_date: {
            gte: Math.floor(Date.now() / 1000)
          }
        }
      }),
      prisma.events.groupBy({
        by: ['association_id'],
        _count: true,
        where: {
          association_id: {
            not: null
          }
        }
      })
    ]);
    
    res.json({
      success: true,
      data: {
        total: totalEvents,
        currentYear: currentYearEvents,
        upcoming: upcomingEvents,
        associations: associationStats
      }
    });
  } catch (error) {
    console.error('Error fetching event stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch event statistics'
    });
  }
});

module.exports = router;
