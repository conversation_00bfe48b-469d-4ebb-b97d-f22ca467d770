import Link from "next/link";
import { Users, Calendar, Trophy, BarChart3, Settings } from "lucide-react";

export default function Home() {
  const modules = [
    {
      title: "Shooters",
      description: "Manage shooter profiles, memberships, and details",
      href: "/shooters",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Events",
      description: "Create and manage shooting events and competitions",
      href: "/events",
      icon: Calendar,
      color: "bg-green-500",
    },
    {
      title: "Results",
      description: "Enter scores, calculate rankings, and manage results",
      href: "/results",
      icon: Trophy,
      color: "bg-yellow-500",
    },
    {
      title: "Reports",
      description: "Generate reports and analytics",
      href: "/reports",
      icon: BarChart3,
      color: "bg-purple-500",
    },
    {
      title: "Settings",
      description: "System configuration and administration",
      href: "/settings",
      icon: Settings,
      color: "bg-gray-500",
    },
  ];

  return (
    <div className="px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome to NRAA Management System
        </h1>
        <p className="text-lg text-gray-600">
          Modern management system for the National Rifle Association of Australia
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {modules.map((module) => {
          const Icon = module.icon;
          return (
            <Link
              key={module.href}
              href={module.href}
              className="group block p-6 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-lg ${module.color} text-white mr-4`}>
                  <Icon className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600">
                  {module.title}
                </h3>
              </div>
              <p className="text-gray-600">{module.description}</p>
            </Link>
          );
        })}
      </div>

      <div className="mt-12 bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">System Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">17,439</div>
            <div className="text-sm text-gray-500">Total Shooters</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">48</div>
            <div className="text-sm text-gray-500">Database Tables</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">Active</div>
            <div className="text-sm text-gray-500">System Status</div>
          </div>
        </div>
      </div>
    </div>
  );
}
